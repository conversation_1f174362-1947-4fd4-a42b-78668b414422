# CV Parser API

A high-performance API for parsing CV/Resume files using LangChain, LangGraph, and OpenAI's GPT-4o-mini model. Features parallel processing architecture for optimal performance and supports PDF, DOC, and DOCX formats with both text extraction and multimodal parsing capabilities.

## Features

### Core Functionality
- **Multiple File Format Support**: PDF, DOC, and DOCX files
- **Raw Text Support**: Parse CV content directly from text input
- **Structured Output**: Returns parsed data in a well-defined Pydantic schema
- **Phone Number Formatting**: Automatic formatting of Vietnamese phone numbers to +84-xxxxxxxxx format
- **Two Parsing Modes**:
  - Text extraction mode: Extracts text first, then parses with LLM
  - Multimodal mode: Direct visual parsing using GPT-4o-mini's vision capabilities
- **PDF to Image Conversion**: Automatic conversion of PDF files to images for multimodal processing
- **Comprehensive Schema**: Extracts personal info, education, work experience, skills, projects, certifications, awards, languages, volunteer experience, and more

### Performance & Architecture
- **🚀 Parallel Processing**: Advanced LangGraph workflow with parallel section parsing for 60-70% faster processing
- **⚡ Optimized Latency**: Concurrent extraction of different CV sections (personal info, education, experience, skills, projects, certifications, additional sections)
- **🔧 Modular Design**: Specialized parsers for each CV section enable independent optimization
- **📊 Comprehensive Testing**: 217 tests ensuring reliability and performance

### Developer Experience
- **🛠️ Modern Tools**: Built with FastAPI, LangChain, LangGraph, and Pydantic
- **🎯 Smart Startup Script**: Enhanced Python-based startup with validation, dependency checks, and cross-platform support
- **📖 Auto-Generated Docs**: Interactive API documentation with OpenAPI/Swagger
- **🧪 Extensive Testing**: Complete test suite with edge cases and integration tests

## Installation

### Prerequisites

- **Python 3.13+** (Required for optimal performance)
- **OpenAI API key** (Get yours at [OpenAI Platform](https://platform.openai.com/api-keys))
- **uv package manager** (Install with: `curl -LsSf https://astral.sh/uv/install.sh | sh`)
- **System dependencies** (required for full functionality):
  - macOS: `brew install poppler antiword`
  - Ubuntu/Debian: `sudo apt-get install poppler-utils antiword`
  - RHEL/CentOS: `sudo yum install poppler-utils antiword`
  - **Note**: `poppler` is required for PDF to image conversion in multimodal mode

### Setup

1. Clone the repository:
```bash
cd adr-cv-parser
```

2. Install Python dependencies using uv:
```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Sync dependencies (installs all production dependencies)
uv sync

# Or sync with dev dependencies
uv sync --dev
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

## Usage

### Starting the API

**Recommended method using the startup script:**
```bash
# Start with default settings (recommended)
uv run python main.py

# Start with custom configuration
uv run python main.py --host 127.0.0.1 --port 9000 --verbose

# Start in production mode (no auto-reload)
uv run python main.py --no-reload --quiet

# View all available options
uv run python main.py --help
```

**Alternative methods:**
```bash
# Using the legacy bash script
./run.sh

# Using uvicorn directly with uv
uv run uvicorn app.api:app --reload --host 0.0.0.0 --port 8080
```

The API will be available at `http://localhost:8080` (or your configured host/port)

### API Endpoints

#### 1. CV Parsing Endpoint

**File Upload (Text Extraction - Default):**
```bash
curl -X POST "http://localhost:8080/parse" \
  -F "file=@your_cv.pdf" \
  -H "accept: application/json"
```

**File Upload (Multimodal Vision Parsing):**
```bash
curl -X POST "http://localhost:8080/parse" \
  -F "file=@your_cv.pdf" \
  -F "multimodal=true" \
  -H "accept: application/json"
```

**Raw Text:**
```bash
curl -X POST "http://localhost:8080/parse" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "John Doe\nSoftware Engineer\nExperience:\n- 5 years Python development\n...",
    "filename": "john_doe_cv.txt"
  }'
```

#### 2. Health Check
```bash
curl "http://localhost:8080/health"
```

### Python Client Example

```python
import requests
import json

# Option 1: Parse CV using file upload (text extraction)
with open("resume.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8080/parse",
        files={"file": ("resume.pdf", f, "application/pdf")}
    )

# Option 2: Parse CV using file upload (multimodal vision)
with open("resume.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8080/parse",
        files={
            "file": ("resume.pdf", f, "application/pdf"),
            "multimodal": (None, "true")
        }
    )

# Option 3: Parse CV using raw text
cv_text = """
John Doe
Software Engineer
Email: <EMAIL>
Phone: (*************

Experience:
- 5 years Python development
- Expert in FastAPI and Django
"""

response = requests.post(
    "http://localhost:8080/parse",
    json={
        "text": cv_text,
        "filename": "john_doe_cv.txt"
    }
)

if response.status_code == 200:
    cv_data = response.json()
    print(json.dumps(cv_data, indent=2))
else:
    print(f"Error: {response.status_code} - {response.text}")
```

## API Documentation

Once the server is running, visit:
- Interactive API docs: `http://localhost:8080/docs`
- ReDoc documentation: `http://localhost:8080/redoc`

## Schema

The API returns data following this structure:

```json
{
  "personal_info": {
    "full_name": "string",
    "email": "string",
    "phone": "string",
    "location": "string",
    "linkedin_url": "string",
    ...
  },
  "summary": "string",
  "education": [...],
  "work_experience": [...],
  "skills": [...],
  "projects": [...],
  "certifications": [...],
  "awards": [...],
  "languages": [...],
  "publications": [...],
  "references": [...],
  "volunteer_experience": [...],
  "extracurricular_activities": [...],
  "metadata": {
    "parsed_at": "string",
    "parser_version": "string",
    "source_file_name": "string"
  }
}
```

See `schema.json` for the complete schema definition.

### Phone Number Formatting

The API automatically formats Vietnamese phone numbers to the standardized +84-xxxxxxxxx format using the `phonenumbers` Python library for robust validation and formatting:

**Supported Input Formats:**
- `0987654321` → `+84-987654321` (valid Vietnamese mobile)
- `+84987654321` → `+84-987654321`
- `84987654321` → `+84-987654321`
- `+84 987 654 321` → `+84-987654321` (removes spaces)
- `0123456789` → `+84-123456789` (fallback for invalid patterns)
- `0123-456-789` → `+84-123456789`
- `(0123) 456 789` → `+84-123456789`

**International Numbers:**
- International phone numbers (e.g., `******-123-4567`) are preserved in their original format
- Only Vietnamese phone numbers are automatically reformatted

**Features:**
- **Robust Validation**: Uses Google's `phonenumbers` library for accurate phone number validation
- **Smart Formatting**: Valid Vietnamese numbers are formatted using international standards
- **Fallback Logic**: Invalid patterns are handled with custom formatting rules
- **International Support**: Preserves international phone number formats
- **Error Handling**: Returns `null` for invalid or empty phone numbers
- **Flexible Input**: Handles various separators (spaces, dashes, parentheses, dots)

## Configuration

Edit `app/config.py` or use environment variables:

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `OPENAI_MODEL`: Model to use (default: "gpt-4o-mini")
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 10MB)
- `TEMPERATURE`: LLM temperature for parsing (default: 0.1)
- `ENABLE_MULTIMODAL`: Enable/disable multimodal parsing (default: true)
- `LOG_LEVEL`: Logging level - DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)

### PDF to Image Conversion Settings
- `PDF_TO_IMAGE_DPI`: DPI for PDF to image conversion (default: 200)
- `PDF_MAX_PAGES`: Maximum pages to process in multimodal mode (default: 10)
- `PDF_IMAGE_FORMAT`: Image format - PNG or JPEG (default: PNG)

## Architecture

### Core Technologies
- **FastAPI** for the high-performance web framework
- **LangChain** for LLM interactions and structured output using Pydantic models
- **LangGraph** for orchestrating the parallel parsing workflow
- **Pydantic** for data validation and serialization
- **OpenAI GPT-4o-mini** with structured output for reliable parsing

### Parallel Processing Workflow
The application uses an advanced parallel processing architecture:

```
Text Input
    ↓
extract_text_node
    ↓
┌──────────────────────────────────────────────────────────┐
│                  Parallel Processing                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │
│  │ Personal    │ │ Education   │ │ Work Exp    │ ...   │
│  │ Info +      │ │ Parser      │ │ Parser      │       │
│  │ Summary     │ │             │ │             │       │
│  │ Parser      │ │             │ │             │       │
│  └─────────────┘ └─────────────┘ └─────────────┘       │
└──────────────────────────────────────────────────────────┘
    ↓
aggregate_results_node
    ↓
validation_node
    ↓
Structured CVData Output (includes extracted summary)
```

### Specialized Parsers
- **PersonalInfoParser**: Extracts contact information, basic details, and professional summary
- **EducationParser**: Processes educational background and qualifications
- **WorkExperienceParser**: Handles professional experience and job history
- **SkillsParser**: Categorizes technical and soft skills
- **ProjectsParser**: Extracts project details and technologies
- **CertificationsParser**: Processes certifications, awards, and publications
- **AdditionalSectionsParser**: Handles languages, volunteer work, activities, and references

### File Processing
- **PDF**: 
  - Text mode: `pdftotext` (poppler-utils) for reliable text extraction
  - Multimodal mode: `pdf2image` with `poppler` for PDF to image conversion
- **DOC**: `antiword` for legacy Microsoft Word documents
- **DOCX**: `python-docx2txt` for modern Word documents
- **Raw Text**: Direct processing without file conversion

### PDF to Image Conversion
For multimodal parsing, PDF files are automatically converted to images:
- **Configurable Quality**: DPI setting controls image resolution (default: 200 DPI)
- **Cost Control**: Maximum pages limit prevents excessive API costs (default: 10 pages)
- **Format Options**: PNG for quality or JPEG for smaller file sizes
- **Error Handling**: Comprehensive error messages for common issues:
  - Missing poppler dependencies
  - Corrupted or invalid PDF files  
  - Password-protected PDFs (not supported)
- **Multi-page Support**: All pages sent in a single OpenAI Vision API request

## Error Handling & Logging

### Error Handling
The API provides detailed error messages for:
- Unsupported file types
- File size limits exceeded
- Parsing failures
- Missing API keys
- Invalid file content

### Comprehensive Logging
The application includes detailed logging for monitoring and debugging:

#### **OpenAI API Error Logging**
- **400 Bad Request**: Invalid base64 data or rate limiting issues
- **401 Unauthorized**: API key validation problems  
- **429 Rate Limit**: Request quota exceeded
- **500 Internal Server Error**: OpenAI service issues
- **Generic Errors**: Comprehensive error tracking with full stack traces

#### **Parser Operation Logging**
- **INFO Level**: File processing start/completion, workflow execution
- **DEBUG Level**: Detailed parser operations, API call timing, extraction counts
- **ERROR Level**: Full exception details with context (filename, operation type)

#### **Log Configuration**
```bash
# Set log level via environment variable
export LOG_LEVEL=DEBUG  # For detailed debugging
export LOG_LEVEL=INFO   # For production (default)
export LOG_LEVEL=ERROR  # For minimal logging

# Example log output
2025-06-17 13:06:18,102 - app.parser - INFO - Starting multimodal CV parsing for file: resume.pdf
2025-06-17 13:06:18,102 - app.parser - DEBUG - MIME type: application/pdf, base64 length: 168072 characters
2025-06-17 13:06:18,102 - app.parser - DEBUG - Sending request to OpenAI Vision API
2025-06-17 13:06:18,345 - app.parser - INFO - Successfully parsed CV with multimodal for file: resume.pdf
```

## Development

### Running Tests
```bash
# Run all tests (217 total)
uv run pytest tests/

# Run tests with coverage
uv run pytest tests/ --cov=app --cov-report=html

# Run specific test modules
uv run pytest tests/test_api.py          # API endpoint tests (24 tests)
uv run pytest tests/test_parser.py       # Parser workflow tests (33 tests)
uv run pytest tests/test_file_processors.py # File processing tests (46 tests, including PDF conversion)
uv run pytest tests/test_main.py         # Startup script tests (48 tests)
uv run pytest tests/test_integration.py  # Integration tests (20 tests)
uv run pytest tests/test_utils.py        # Utility function tests (31 tests, including phone formatting)
uv run pytest tests/test_models.py       # Data model tests (35 tests)

# Run tests with verbose output
uv run pytest tests/ -v

# Test PDF to image conversion specifically
uv run pytest tests/test_file_processors.py::TestPDFToImageConverter -v

# Run linting and formatting
uv run ruff check .
uv run ruff format .
```

### Adding New Features
1. Update the Pydantic models in `app/models.py`
2. Modify the parsing logic in `app/parser.py`
3. Update the API endpoints in `app/api.py`
4. Add comprehensive tests for new functionality

## Performance

### Parallel Processing Benefits
The parallel architecture provides significant performance improvements:

- **60-70% faster processing** compared to sequential parsing
- **Concurrent section extraction**: All CV sections are processed simultaneously, including summary extraction
- **Optimized LLM calls**: Each specialized parser uses focused prompts for better accuracy
- **Scalable design**: Easy to add new section parsers without affecting existing ones
- **Error isolation**: Failures in one section don't affect others

### Benchmarks
- **Small CV (1-2 pages)**: ~2-3 seconds
- **Medium CV (3-4 pages)**: ~4-6 seconds  
- **Large CV (5+ pages)**: ~8-12 seconds
- **Multimodal parsing**: +20-30% processing time for visual analysis

*Note: Times may vary based on OpenAI API response times and content complexity*

## Startup Script Features

The `main.py` script provides enhanced functionality compared to the legacy `run.sh`:

### Validation & Checks
- ✅ **Environment validation**: Checks for `.env` file and OpenAI API key
- ✅ **Dependency verification**: Ensures `uv` is installed and syncs dependencies
- ✅ **System dependency checks**: Verifies `pdftotext` and `antiword` availability
- ✅ **Application structure**: Validates all required files are present

### Enhanced User Experience  
- 🎨 **Colored output**: Visual indicators for status and errors
- 📝 **Verbose mode**: Detailed debugging information with `--verbose`
- 🔇 **Quiet mode**: Minimal output with `--quiet`
- 🚀 **Auto-install**: Optional automatic dependency installation
- ⚙️ **Flexible configuration**: Customizable host, port, and reload settings

### Cross-Platform Support
- 🍎 **macOS**: Native support with Homebrew install instructions
- 🐧 **Linux**: Support for Ubuntu/Debian and RHEL/CentOS
- 🪟 **Windows**: Cross-platform Python implementation

### Example Usage
```bash
# Development mode with debugging
uv run python main.py --verbose --reload

# Production mode
uv run python main.py --host 0.0.0.0 --port 80 --no-reload --quiet

# Auto-install missing dependencies
uv run python main.py --auto-install

# Custom configuration
uv run python main.py --host 127.0.0.1 --port 9000 --no-color
```

## License

[Add your license here]

## Contributing

[Add contribution guidelines]