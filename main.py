"""
CV Parser API Startup Script

A Python-based replacement for run.sh with enhanced functionality,
better error handling, and cross-platform compatibility.
"""

import argparse
import signal
import sys

try:
    import uvicorn
except ImportError:
    print("Error: uvicorn not found. Please install dependencies first.")
    sys.exit(1)


class Colors:
    """ANSI color codes for terminal output."""

    RED = "\033[91m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    MAGENTA = "\033[95m"
    CYAN = "\033[96m"
    WHITE = "\033[97m"
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"
    END = "\033[0m"

    @classmethod
    def disable(cls):
        """Disable colors for non-terminal output."""
        cls.RED = cls.GREEN = cls.YELLOW = cls.BLUE = ""
        cls.MAGENTA = cls.CYAN = cls.WHITE = cls.BOLD = ""
        cls.UNDERLINE = cls.END = ""


class CVParserStarter:
    """Main class for starting the CV Parser API."""

    def __init__(self, args: argparse.Namespace):
        self.args = args
        self.verbose = args.verbose
        self.quiet = args.quiet

        # Disable colors if output is not a terminal or explicitly requested
        if not sys.stdout.isatty() or args.no_color:
            Colors.disable()

    def log(self, message: str, color: str = "", level: str = "info"):
        """Log a message with optional color and level."""
        if self.quiet and level == "info":
            return

        prefix = ""
        if level == "error":
            prefix = f"{Colors.RED}Error:{Colors.END} "
        elif level == "warning":
            prefix = f"{Colors.YELLOW}Warning:{Colors.END} "
        elif level == "success":
            prefix = f"{Colors.GREEN}✓{Colors.END} "

        print(f"{prefix}{color}{message}{Colors.END}")

    def log_verbose(self, message: str, color: str = ""):
        """Log a verbose message only if verbose mode is enabled."""
        if self.verbose:
            self.log(f"[DEBUG] {message}", color)

    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""

        def signal_handler(signum, frame):
            self.log("\nShutting down gracefully...", Colors.YELLOW)
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def start_server(self) -> bool:
        """Start the uvicorn server."""
        self.log("Starting CV Parser API...", Colors.BOLD + Colors.BLUE)
        self.log("=" * 50, Colors.BLUE)

        # Setup signal handlers
        self.setup_signal_handlers()

        # Prepare uvicorn configuration
        config = {
            "app": "app.api:app",
            "host": self.args.host,
            "port": self.args.port,
            "reload": self.args.reload,
            "log_level": "debug" if self.verbose else "info",
        }

        self.log("Server configuration:", Colors.CYAN)
        self.log(f"  Host: {config['host']}")
        self.log(f"  Port: {config['port']}")
        self.log(f"  Reload: {config['reload']}")
        self.log(f"  Log level: {config['log_level']}")

        # Display URLs
        self.log("")
        self.log("API will be available at:", Colors.GREEN + Colors.BOLD)
        self.log(f"  Main API: http://{config['host']}:{config['port']}")
        self.log(f"  Documentation: http://{config['host']}:{config['port']}/docs")
        self.log(f"  OpenAPI Schema: http://{config['host']}:{config['port']}/openapi.json")
        self.log("")
        self.log("Press Ctrl+C to stop the server", Colors.YELLOW)
        self.log("")

        try:
            # Start the server
            uvicorn.run(**config)
            return True
        except KeyboardInterrupt:
            self.log("Server stopped by user", Colors.YELLOW)
            return True
        except Exception as e:
            self.log(f"Failed to start server: {e}", level="error")
            return False

    def run(self) -> int:
        """Main execution method."""
        try:
            # Start the server directly
            if not self.start_server():
                return 1

            return 0

        except KeyboardInterrupt:
            self.log("\nOperation cancelled by user", Colors.YELLOW)
            return 130
        except Exception as e:
            self.log(f"Unexpected error: {e}", level="error")
            if self.verbose:
                import traceback

                traceback.print_exc()
            return 1


def create_arg_parser() -> argparse.ArgumentParser:
    """Create and configure argument parser."""
    parser = argparse.ArgumentParser(
        description="CV Parser API Startup Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Start with default settings
  python main.py --host 0.0.0.0 --port 8080  # Custom host and port
  python main.py --verbose               # Enable verbose logging
  python main.py --no-reload             # Disable auto-reload
        """,
    )

    # Server configuration
    parser.add_argument(
        "--host", default="0.0.0.0", help="Host to bind the server to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", type=int, default=8080, help="Port to bind the server to (default: 8080)"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        default=True,
        help="Enable auto-reload for development (default: True)",
    )
    parser.add_argument(
        "--no-reload", action="store_false", dest="reload", help="Disable auto-reload"
    )

    # Logging and output
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress non-error output")
    parser.add_argument("--no-color", action="store_true", help="Disable colored output")

    return parser


def main():
    """Main entry point."""
    parser = create_arg_parser()
    args = parser.parse_args()

    # Validate arguments
    if args.verbose and args.quiet:
        print("Error: --verbose and --quiet are mutually exclusive")
        return 1

    if args.port < 1 or args.port > 65535:
        print("Error: Port must be between 1 and 65535")
        return 1

    # Create and run the starter
    starter = CVParserStarter(args)
    return starter.run()


if __name__ == "__main__":
    sys.exit(main())
