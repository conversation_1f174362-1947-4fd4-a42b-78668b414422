{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "<PERSON><PERSON>(uv run ruff:*)", "Bash(uv sync:*)", "Bash(uv add:*)", "Bash(uv run pytest:*)", "<PERSON><PERSON>(uv run:*)", "Bash(grep:*)", "<PERSON><PERSON>(uv list:*)", "<PERSON><PERSON>(uv pip:*)", "Bash(find:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(rg:*)", "<PERSON><PERSON>(timeout 5 uv run:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)"], "deny": []}}