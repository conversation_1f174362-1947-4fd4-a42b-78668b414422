"""Test configuration and fixtures for CV Parser API tests."""

import tempfile
from io import BytesIO
from pathlib import Path
from unittest.mock import AsyncMock, <PERSON>ck

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

from app.api import app
from app.config import Settings
from app.models import CVData, Metadata, PersonalInfo


@pytest.fixture
def test_settings() -> Settings:
    """Create test settings with safe defaults."""
    settings = Settings()
    settings.OPENAI_API_KEY = "test-api-key"
    settings.OPENAI_MODEL = "gpt-4o-mini"
    settings.ENABLE_MULTIMODAL = True
    settings.MAX_FILE_SIZE = 1024 * 1024  # 1MB for tests
    settings.ALLOWED_EXTENSIONS = {".pdf", ".doc", ".docx"}
    return settings


@pytest.fixture
def client() -> TestClient:
    """Create FastAPI test client."""
    return TestClient(app)


@pytest.fixture
async def async_client() -> AsyncClient:
    """Create async HTTP client for testing."""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_cv_text() -> str:
    """Sample CV text for testing."""
    return """
John Doe
Software Engineer
Email: <EMAIL>
Phone: (*************
Location: San Francisco, CA

SUMMARY
Experienced software engineer with 5+ years in backend development.

EXPERIENCE
Senior Software Engineer
Tech Corp | 2020 - Present
• Developed scalable APIs using Python and FastAPI
• Led team of 4 engineers
• Improved system performance by 40%

Software Engineer
StartupCo | 2018 - 2020
• Built microservices architecture
• Implemented CI/CD pipelines

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley | 2014 - 2018
GPA: 3.8/4.0

SKILLS
• Programming: Python, JavaScript, SQL
• Frameworks: FastAPI, React, Django
• Tools: Docker, Kubernetes, AWS

PROJECTS
E-commerce Platform
• Built full-stack e-commerce application
• Technologies: Python, React, PostgreSQL
• URL: https://github.com/johndoe/ecommerce

CERTIFICATIONS
AWS Certified Solutions Architect
Amazon Web Services | 2021
Credential ID: AWS-12345

LANGUAGES
English: Native
Spanish: Conversational
"""


@pytest.fixture
def sample_parsed_cv() -> CVData:
    """Sample parsed CV data for testing."""
    return CVData(
        personal_info=PersonalInfo(
            full_name="John Doe",
            email="<EMAIL>",
            phone="+84-123456789",
            location="San Francisco, CA",
        ),
        summary="Experienced software engineer with 5+ years in backend development.",
        metadata=Metadata(
            parsed_at="2024-01-01T00:00:00",
            parser_version="1.1.3",
            source_file_name="test_cv.pdf",
        ),
    )


@pytest.fixture
def create_test_file():
    """Factory fixture for creating test files."""

    def _create_file(content: bytes, filename: str, suffix: str = ".pdf") -> BytesIO:
        """Create a test file with given content."""
        file_obj = BytesIO(content)
        file_obj.name = filename
        return file_obj

    return _create_file


@pytest.fixture
def sample_pdf_content() -> bytes:
    """Sample PDF file content for testing."""
    # Simple PDF header - this won't be a valid PDF but will have correct extension
    return b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n%%EOF"


@pytest.fixture
def sample_docx_content() -> bytes:
    """Sample DOCX file content for testing."""
    # Minimal DOCX-like content (ZIP archive header)
    return b"PK\x03\x04\x14\x00\x00\x00\x08\x00"


@pytest.fixture
def sample_doc_content() -> bytes:
    """Sample DOC file content for testing."""
    # MS Word document signature
    return b"\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1"


@pytest.fixture
def large_file_content() -> bytes:
    """Large file content for size testing."""
    return b"x" * (11 * 1024 * 1024)  # 11MB file


@pytest.fixture
def temp_file():
    """Create a temporary file for testing."""

    def _create_temp_file(content: bytes, suffix: str = ".pdf") -> str:
        """Create a temporary file with given content."""
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
            tmp.write(content)
            return tmp.name

    yield _create_temp_file

    # Cleanup is handled by the test itself


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response."""

    def _create_response(cv_data: CVData) -> Mock:
        """Create a mock OpenAI response."""
        mock_response = Mock()
        mock_response.ainvoke = AsyncMock(return_value=cv_data)
        return mock_response

    return _create_response


@pytest.fixture
def mock_file_processors():
    """Mock file processors for testing."""
    from unittest.mock import patch

    with patch("app.file_processors.process_file") as mock_process:
        with patch("app.file_processors.get_file_base64") as mock_base64:
            mock_process.return_value = ("Sample CV text content", "test_cv.pdf")
            mock_base64.return_value = ("base64content", "application/pdf")
            yield {
                "process_file": mock_process,
                "get_file_base64": mock_base64,
            }


@pytest.fixture
def mock_cv_parser():
    """Mock CV parser for testing."""
    from unittest.mock import patch

    with patch("app.parser.cv_parser_workflow") as mock_workflow:
        yield mock_workflow


@pytest.fixture(autouse=True)
def cleanup_temp_files():
    """Cleanup temporary files after each test."""
    yield
    # Cleanup any remaining temp files
    temp_dir = Path(tempfile.gettempdir())
    for file_path in temp_dir.glob("tmp*"):
        try:
            file_path.unlink()
        except (OSError, PermissionError):
            pass  # File might be in use or already deleted


@pytest.fixture
def mock_settings():
    """Mock settings for testing different configurations."""
    from unittest.mock import patch

    def _mock_setting(setting_name: str, value):
        """Mock a specific setting."""
        return patch(f"app.config.settings.{setting_name}", value)

    return _mock_setting


class MockUploadFile:
    """Mock UploadFile for testing."""

    def __init__(self, content: bytes, filename: str, content_type: str = "application/pdf"):
        self.file = BytesIO(content)
        self.filename = filename
        self.content_type = content_type
        self.size = len(content)

    async def read(self) -> bytes:
        """Read file content."""
        return self.file.read()

    async def seek(self, position: int) -> None:
        """Seek to position in file."""
        self.file.seek(position)


@pytest.fixture
def mock_upload_file():
    """Factory for creating mock upload files."""
    return MockUploadFile


# Performance testing fixtures
@pytest.fixture
def benchmark_data():
    """Data for performance benchmarking."""
    return {
        "small_cv": "John Doe\nSoftware Engineer\n" * 10,
        "medium_cv": "John Doe\nSoftware Engineer\n" * 100,
        "large_cv": "John Doe\nSoftware Engineer\n" * 1000,
    }


# Error simulation fixtures
@pytest.fixture
def simulate_openai_error():
    """Simulate OpenAI API errors."""
    from unittest.mock import patch

    def _create_error(error_type: str):
        """Create different types of OpenAI errors."""
        if error_type == "timeout":
            return patch("app.parser.CVParser.parse_from_text", side_effect=TimeoutError())
        elif error_type == "api_error":
            return patch(
                "app.parser.CVParser.parse_from_text",
                side_effect=Exception("OpenAI API Error"),
            )
        elif error_type == "rate_limit":
            return patch(
                "app.parser.CVParser.parse_from_text",
                side_effect=Exception("Rate limit exceeded"),
            )

    return _create_error
