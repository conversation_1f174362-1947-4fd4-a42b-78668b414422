"""Tests for main.py startup script."""

import argparse
import sys
from pathlib import Path
from unittest.mock import patch

# Import the main module
sys.path.insert(0, str(Path(__file__).parent.parent))
from main import Colors, CVParserStarter, create_arg_parser, main


class TestColors:
    """Test color utilities."""

    def test_colors_have_values(self):
        """Test that color constants are defined."""
        assert Colors.RED
        assert Colors.GREEN
        assert Colors.YELLOW
        assert Colors.BLUE
        assert Colors.END

    def test_disable_colors(self):
        """Test disabling colors."""
        # Store original values
        original_red = Colors.RED
        original_green = Colors.GREEN

        Colors.disable()

        assert Colors.RED == ""
        assert Colors.GREEN == ""
        assert Colors.END == ""

        # Restore for other tests
        Colors.RED = original_red
        Colors.GREEN = original_green


class TestArgParser:
    """Test argument parsing."""

    def test_create_arg_parser(self):
        """Test argument parser creation."""
        parser = create_arg_parser()
        assert isinstance(parser, argparse.ArgumentParser)

    def test_default_arguments(self):
        """Test default argument values."""
        parser = create_arg_parser()
        args = parser.parse_args([])

        assert args.host == "0.0.0.0"
        assert args.port == 8080
        assert args.reload is True
        assert args.verbose is False
        assert args.quiet is False
        assert args.no_color is False

    def test_custom_arguments(self):
        """Test custom argument parsing."""
        parser = create_arg_parser()
        args = parser.parse_args(
            ["--host", "127.0.0.1", "--port", "9000", "--no-reload", "--verbose", "--no-color"]
        )

        assert args.host == "127.0.0.1"
        assert args.port == 9000
        assert args.reload is False
        assert args.verbose is True
        assert args.no_color is True

    def test_quiet_mode(self):
        """Test quiet mode argument."""
        parser = create_arg_parser()
        args = parser.parse_args(["--quiet"])

        assert args.quiet is True


class TestCVParserStarter:
    """Test the main CVParserStarter class."""

    def create_starter(self, **kwargs):
        """Helper to create a CVParserStarter instance."""
        default_args = {
            "host": "0.0.0.0",
            "port": 8080,
            "reload": True,
            "verbose": False,
            "quiet": False,
            "no_color": True,  # Disable colors for tests
        }
        default_args.update(kwargs)
        args = argparse.Namespace(**default_args)
        return CVParserStarter(args)

    def test_initialization(self):
        """Test CVParserStarter initialization."""
        starter = self.create_starter()
        assert starter.args is not None
        assert starter.verbose is False
        assert starter.quiet is False

    def test_log_normal(self, capsys):
        """Test normal logging."""
        starter = self.create_starter()
        starter.log("Test message")

        captured = capsys.readouterr()
        assert "Test message" in captured.out

    def test_log_quiet_mode(self, capsys):
        """Test logging in quiet mode."""
        starter = self.create_starter(quiet=True)
        starter.log("Test message", level="info")

        captured = capsys.readouterr()
        assert captured.out == ""

    def test_log_error_in_quiet_mode(self, capsys):
        """Test error logging still works in quiet mode."""
        starter = self.create_starter(quiet=True)
        starter.log("Error message", level="error")

        captured = capsys.readouterr()
        assert "Error message" in captured.out

    def test_log_verbose(self, capsys):
        """Test verbose logging."""
        starter = self.create_starter(verbose=True)
        starter.log_verbose("Debug message")

        captured = capsys.readouterr()
        assert "[DEBUG] Debug message" in captured.out

    def test_log_verbose_disabled(self, capsys):
        """Test verbose logging when disabled."""
        starter = self.create_starter(verbose=False)
        starter.log_verbose("Debug message")

        captured = capsys.readouterr()
        assert captured.out == ""

    @patch("uvicorn.run")
    @patch.object(CVParserStarter, "setup_signal_handlers")
    def test_start_server_success(self, mock_signal_setup, mock_uvicorn_run):
        """Test successful server start."""
        starter = self.create_starter()
        result = starter.start_server()

        assert result is True
        mock_signal_setup.assert_called_once()
        mock_uvicorn_run.assert_called_once()

    @patch("uvicorn.run")
    @patch.object(CVParserStarter, "setup_signal_handlers")
    def test_start_server_keyboard_interrupt(self, mock_signal_setup, mock_uvicorn_run):
        """Test server start with keyboard interrupt."""
        mock_uvicorn_run.side_effect = KeyboardInterrupt()

        starter = self.create_starter()
        result = starter.start_server()

        assert result is True

    @patch("uvicorn.run")
    @patch.object(CVParserStarter, "setup_signal_handlers")
    def test_start_server_exception(self, mock_signal_setup, mock_uvicorn_run):
        """Test server start with exception."""
        mock_uvicorn_run.side_effect = Exception("Server error")

        starter = self.create_starter()
        result = starter.start_server()

        assert result is False

    @patch.object(CVParserStarter, "start_server")
    def test_run_success(self, mock_start_server):
        """Test successful run method."""
        mock_start_server.return_value = True

        starter = self.create_starter()
        result = starter.run()

        assert result == 0

    @patch.object(CVParserStarter, "start_server")
    def test_run_server_failure(self, mock_start_server):
        """Test run method with server start failure."""
        mock_start_server.return_value = False

        starter = self.create_starter()
        result = starter.run()

        assert result == 1

    @patch.object(CVParserStarter, "start_server")
    def test_run_keyboard_interrupt(self, mock_start_server):
        """Test run method with keyboard interrupt."""
        mock_start_server.side_effect = KeyboardInterrupt()

        starter = self.create_starter()
        result = starter.run()

        assert result == 130

    @patch.object(CVParserStarter, "start_server")
    def test_run_unexpected_exception(self, mock_start_server):
        """Test run method with unexpected exception."""
        mock_start_server.side_effect = Exception("Unexpected error")

        starter = self.create_starter()
        result = starter.run()

        assert result == 1


class TestMainFunction:
    """Test the main function."""

    def test_main_with_conflicting_args(self, capsys):
        """Test main with conflicting verbose and quiet arguments."""
        with patch("sys.argv", ["main.py", "--verbose", "--quiet"]):
            result = main()
            assert result == 1

        captured = capsys.readouterr()
        assert "--verbose and --quiet are mutually exclusive" in captured.out

    def test_main_with_invalid_port(self, capsys):
        """Test main with invalid port argument."""
        with patch("sys.argv", ["main.py", "--port", "70000"]):
            result = main()
            assert result == 1

        captured = capsys.readouterr()
        assert "Port must be between 1 and 65535" in captured.out

    @patch.object(CVParserStarter, "run")
    def test_main_success(self, mock_run):
        """Test successful main execution."""
        mock_run.return_value = 0

        with patch("sys.argv", ["main.py"]):
            result = main()
            assert result == 0

    @patch.object(CVParserStarter, "run")
    def test_main_failure(self, mock_run):
        """Test main execution with failure."""
        mock_run.return_value = 1

        with patch("sys.argv", ["main.py"]):
            result = main()
            assert result == 1


class TestIntegration:
    """Integration tests for the main module."""

    @patch("uvicorn.run")
    def test_basic_startup_flow(self, mock_uvicorn_run):
        """Test the basic startup flow."""
        with patch("sys.argv", ["main.py", "--no-color"]):
            starter_run_called = False
            original_run = CVParserStarter.run

            def mock_starter_run(self):
                nonlocal starter_run_called
                starter_run_called = True
                # Just test basic initialization
                return 0

            with patch.object(CVParserStarter, "run", mock_starter_run):
                result = main()

        assert starter_run_called
        assert result == 0

    def test_argument_combinations(self):
        """Test various argument combinations."""
        test_cases = [
            (["--host", "127.0.0.1"], {"host": "127.0.0.1"}),
            (["--port", "9000"], {"port": 9000}),
            (["--no-reload"], {"reload": False}),
            (["--verbose"], {"verbose": True}),
            (["--quiet"], {"quiet": True}),
            (["--no-color"], {"no_color": True}),
        ]

        for args, expected in test_cases:
            parser = create_arg_parser()
            parsed_args = parser.parse_args(args)

            for key, value in expected.items():
                assert getattr(parsed_args, key) == value


# Performance and edge case tests
class TestEdgeCases:
    """Test edge cases and error conditions."""

    def test_colors_disable_idempotent(self):
        """Test that disabling colors multiple times is safe."""
        Colors.disable()
        Colors.disable()

        assert Colors.RED == ""
        assert Colors.GREEN == ""

    def test_very_long_log_message(self, capsys):
        """Test handling of very long log messages."""
        starter = CVParserStarter(argparse.Namespace(verbose=False, quiet=False, no_color=True))

        long_message = "A" * 10000
        starter.log(long_message)

        captured = capsys.readouterr()
        assert long_message in captured.out

    def test_uvicorn_config_generation(self):
        """Test uvicorn configuration generation."""
        starter = CVParserStarter(
            argparse.Namespace(
                host="127.0.0.1", port=9000, reload=False, verbose=True, quiet=False, no_color=True
            )
        )

        # Test that the starter generates correct config
        assert starter.args.host == "127.0.0.1"
        assert starter.args.port == 9000
        assert starter.args.reload is False
        assert starter.verbose is True
