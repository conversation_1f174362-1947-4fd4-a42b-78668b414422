"""Tests for CV parser functionality."""

from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch

import pytest

from app.models import (
    Award,
    Certification,
    CVData,
    Education,
    Language,
    PersonalInfo,
    Project,
    Skill,
    WorkExperience,
)
from app.parser import (
    CVParser,
    CVParserState,
    aggregate_results_node,
    create_cv_parser_workflow,
    extract_text_node,
    parse_personal_info_node,
    validation_node,
)


class TestCVParser:
    """Test the CVParser class."""

    @patch("app.parser.ChatOpenAI")
    def test_cv_parser_initialization(self, mock_chat_openai):
        """Test CVParser initialization."""
        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        assert parser.llm == mock_llm
        assert hasattr(parser, "text_prompt")
        assert hasattr(parser, "multimodal_prompt_text")
        mock_chat_openai.assert_called_once()

    @patch("app.parser.ChatOpenAI")
    def test_create_text_chain(self, mock_chat_openai):
        """Test text chain creation."""
        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()
        chain = parser.create_text_chain()

        assert chain is not None

    @patch("app.parser.ChatOpenAI")
    def test_create_multimodal_chain(self, mock_chat_openai):
        """Test multimodal chain creation."""
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value = "multimodal_chain"
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()
        chain = parser.create_multimodal_chain()

        assert chain == "multimodal_chain"
        mock_llm.with_structured_output.assert_called_once_with(CVData)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    @patch("app.parser.datetime")
    async def test_parse_from_text_success(self, mock_datetime, mock_chat_openai):
        """Test successful text parsing."""
        # Setup mocks
        mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T00:00:00"

        mock_chain = AsyncMock()
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>")
        )
        mock_chain.ainvoke.return_value = parsed_data

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        # Mock the chain creation
        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text("Sample CV text", "test.pdf")

        assert result.personal_info.full_name == "John Doe"
        assert result.metadata is not None
        assert result.metadata.source_file_name == "test.pdf"
        assert result.metadata.parser_version == "1.1.3"
        mock_chain.ainvoke.assert_called_once_with("Sample CV text")

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parse_from_text_error(self, mock_chat_openai):
        """Test text parsing with error."""
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Parsing failed")

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    @patch("app.parser.datetime")
    @patch("app.parser.HumanMessage")
    @patch("app.file_processors.PDFToImageConverter.convert_pdf_to_images")
    async def test_parse_from_file_success(
        self, mock_convert_pdf, mock_human_message, mock_datetime, mock_chat_openai
    ):
        """Test successful multimodal file parsing."""
        # Setup mocks
        mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T00:00:00"

        # Mock PDF to image conversion
        mock_convert_pdf.return_value = [
            "aW1hZ2UxZGF0YQ==",
            "aW1hZ2UyZGF0YQ==",
        ]  # Valid base64 strings

        mock_chain = AsyncMock()
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="Jane Doe", email="<EMAIL>")
        )
        mock_chain.ainvoke.return_value = parsed_data

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_multimodal_chain", return_value=mock_chain):
            # Use a valid base64 string
            import base64

            valid_base64 = base64.b64encode(b"fake pdf content").decode("utf-8")
            result = await parser.parse_from_file(valid_base64, "application/pdf", "test.pdf")

        assert result.personal_info.full_name == "Jane Doe"
        assert result.metadata.source_file_name == "test.pdf"
        mock_chain.ainvoke.assert_called_once()
        mock_convert_pdf.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    @patch("app.file_processors.PDFToImageConverter.convert_pdf_to_images")
    async def test_parse_from_file_error(self, mock_convert_pdf, mock_chat_openai):
        """Test multimodal file parsing with error."""
        # Mock PDF to image conversion
        mock_convert_pdf.return_value = ["aW1hZ2UxZGF0YQ=="]

        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Multimodal parsing failed")

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_multimodal_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                # Use a valid base64 string
                import base64

                valid_base64 = base64.b64encode(b"fake pdf content").decode("utf-8")
                await parser.parse_from_file(valid_base64, "application/pdf", "test.pdf")

        assert "Error parsing CV with multimodal" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parse_from_text_empty_response(self, mock_chat_openai):
        """Test parsing with empty/invalid response from LLM."""
        mock_chain = AsyncMock()
        mock_chain.ainvoke.return_value = None

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception):
                await parser.parse_from_text("Sample CV text", "test.pdf")


class TestWorkflowNodes:
    """Test LangGraph workflow nodes."""

    @pytest.mark.asyncio
    async def test_extract_text_node_text_mode_success(self):
        """Test extract_text_node with valid text mode state."""
        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await extract_text_node(state)
        assert result == {}  # No error

    @pytest.mark.asyncio
    async def test_extract_text_node_text_mode_no_text(self):
        """Test extract_text_node with text mode but no text."""
        state: CVParserState = {
            "input_text": None,
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await extract_text_node(state)
        assert "errors" in result
        assert "No text provided for text mode parsing" in result["errors"][0]

    @pytest.mark.asyncio
    async def test_extract_text_node_multimodal_mode_success(self):
        """Test extract_text_node with valid multimodal mode state."""
        state: CVParserState = {
            "input_text": None,
            "input_file_base64": "base64content",
            "filename": "test.pdf",
            "parsed_data": None,
            "errors": [],
            "mode": "multimodal",
        }

        result = await extract_text_node(state)
        assert result == {}  # No error

    @pytest.mark.asyncio
    async def test_extract_text_node_multimodal_mode_no_file(self):
        """Test extract_text_node with multimodal mode but no file data."""
        state: CVParserState = {
            "input_text": None,
            "input_file_base64": None,
            "filename": "test.pdf",
            "parsed_data": None,
            "errors": [],
            "mode": "multimodal",
        }

        result = await extract_text_node(state)
        assert "errors" in result
        assert "No file data provided for multimodal parsing" in result["errors"][0]

    @pytest.mark.asyncio
    @patch("app.parser.PersonalInfoParser")
    async def test_parse_personal_info_node_text_mode_success(self, mock_parser_class):
        """Test parse_personal_info_node with successful text parsing."""
        # Setup mock parser with PersonalInfoWithSummary response
        from app.parser import PersonalInfoWithSummary

        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        combined_result = PersonalInfoWithSummary(
            personal_info=personal_info,
            summary="Experienced software engineer with 5+ years in Python development.",
        )
        mock_parser = Mock()
        mock_parser.parse_from_text = AsyncMock(return_value=combined_result)
        mock_parser_class.return_value = mock_parser

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await parse_personal_info_node(state)

        assert "personal_info_result" in result
        assert "summary_result" in result
        assert result["personal_info_result"].full_name == "John Doe"
        assert (
            result["summary_result"]
            == "Experienced software engineer with 5+ years in Python development."
        )
        mock_parser.parse_from_text.assert_called_once_with("Sample CV text")

    @pytest.mark.asyncio
    @patch("app.parser.PersonalInfoParser")
    async def test_parse_personal_info_node_parsing_error(self, mock_parser_class):
        """Test parse_personal_info_node with parsing error."""
        # Setup mock parser to raise error
        mock_parser = Mock()
        mock_parser.parse_from_text = AsyncMock(side_effect=Exception("Parsing failed"))
        mock_parser_class.return_value = mock_parser

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await parse_personal_info_node(state)

        assert "errors" in result
        assert "Personal info parsing failed" in result["errors"][0]

    @pytest.mark.asyncio
    async def test_aggregate_results_node_success(self):
        """Test aggregate_results_node with successful aggregation."""
        from app.models import Education, Skill, WorkExperience

        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        education = [Education(school="University", degree="Bachelor")]
        work_experience = [WorkExperience(company="Company", job_title="Developer")]
        skills = [Skill(name="Python", category="Programming")]

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "personal_info_result": personal_info,
            "education_result": education,
            "work_experience_result": work_experience,
            "skills_result": skills,
            "projects_result": [],
            "certifications_result": {},
            "additional_sections_result": {},
        }

        result = await aggregate_results_node(state)

        assert "parsed_data" in result
        parsed_data = result["parsed_data"]
        assert parsed_data.personal_info.full_name == "John Doe"
        assert len(parsed_data.education) == 1
        assert len(parsed_data.work_experience) == 1
        assert len(parsed_data.skills) == 1

    def test_validation_node_success(self):
        """Test validation_node with valid parsed data."""
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>")
        )

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": parsed_data,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert result == {}  # No error

    def test_validation_node_with_existing_error(self):
        """Test validation_node when state already has error."""
        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": ["Previous error"],
            "mode": "text",
        }

        result = validation_node(state)
        assert result == {}  # Returns empty dict when errors exist

    def test_validation_node_no_parsed_data(self):
        """Test validation_node with no parsed data."""
        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert "errors" in result
        assert "No parsed data available" in result["errors"][0]

    def test_validation_node_no_personal_info(self):
        """Test validation_node with missing personal info."""
        parsed_data = CVData(summary="Some summary")  # No personal_info

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": parsed_data,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert "errors" in result
        assert "Could not extract basic personal information" in result["errors"][0]

    def test_validation_node_no_full_name(self):
        """Test validation_node with personal info but no full name."""
        parsed_data = CVData(
            personal_info=PersonalInfo(email="<EMAIL>")  # No full_name
        )

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": parsed_data,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert "errors" in result
        assert "Could not extract basic personal information" in result["errors"][0]


class TestWorkflowIntegration:
    """Test the complete LangGraph workflow."""

    def test_create_cv_parser_workflow(self):
        """Test workflow creation."""
        workflow = create_cv_parser_workflow()
        assert workflow is not None

    @pytest.mark.asyncio
    @patch("app.parser.PersonalInfoParser")
    @patch("app.parser.EducationParser")
    @patch("app.parser.WorkExperienceParser")
    @patch("app.parser.SkillsParser")
    @patch("app.parser.ProjectsParser")
    @patch("app.parser.CertificationsParser")
    @patch("app.parser.AdditionalSectionsParser")
    async def test_workflow_execution_success(
        self,
        mock_additional,
        mock_certs,
        mock_projects,
        mock_skills,
        mock_work,
        mock_education,
        mock_personal,
    ):
        """Test complete workflow execution with success."""
        # Setup mock parsers for each section
        from app.parser import PersonalInfoWithSummary

        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        combined_result = PersonalInfoWithSummary(
            personal_info=personal_info,
            summary="Experienced software engineer with 5+ years in Python development.",
        )
        mock_personal_parser = Mock()
        mock_personal_parser.parse_from_text = AsyncMock(return_value=combined_result)
        mock_personal.return_value = mock_personal_parser

        # Setup other mock parsers
        for mock_parser_class, mock_parser_var in [
            (mock_education, "mock_edu_parser"),
            (mock_work, "mock_work_parser"),
            (mock_skills, "mock_skills_parser"),
            (mock_projects, "mock_projects_parser"),
            (mock_certs, "mock_certs_parser"),
            (mock_additional, "mock_additional_parser"),
        ]:
            mock_parser = Mock()
            mock_parser.parse_from_text = AsyncMock(return_value=[])
            mock_parser_class.return_value = mock_parser

        # Special case for parsers that return dicts
        mock_certs_parser = Mock()
        mock_certs_parser.parse_from_text = AsyncMock(return_value={})
        mock_certs.return_value = mock_certs_parser

        mock_additional_parser = Mock()
        mock_additional_parser.parse_from_text = AsyncMock(return_value={})
        mock_additional.return_value = mock_additional_parser

        # Import the actual workflow
        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "parsed_data" in result
        assert result["parsed_data"].personal_info.full_name == "John Doe"
        assert result.get("error") is None

    @pytest.mark.asyncio
    async def test_workflow_execution_text_validation_error(self):
        """Test workflow execution with text validation error."""
        from app.parser import cv_parser_workflow

        # State with no input text for text mode
        initial_state: CVParserState = {
            "input_text": None,
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "errors" in result
        assert "No text provided for text mode parsing" in result["errors"][0]

    @pytest.mark.asyncio
    @patch("app.parser.PersonalInfoParser")
    async def test_workflow_execution_parsing_error(self, mock_personal_parser_class):
        """Test workflow execution with parsing error."""
        # Setup mock parser to raise error
        mock_parser = Mock()
        mock_parser.parse_from_text = AsyncMock(side_effect=Exception("Parsing failed"))
        mock_personal_parser_class.return_value = mock_parser

        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "errors" in result
        assert "Personal info parsing failed" in result["errors"][0]

    @pytest.mark.asyncio
    @patch("app.parser.PersonalInfoParser")
    @patch("app.parser.EducationParser")
    @patch("app.parser.WorkExperienceParser")
    @patch("app.parser.SkillsParser")
    @patch("app.parser.ProjectsParser")
    @patch("app.parser.CertificationsParser")
    @patch("app.parser.AdditionalSectionsParser")
    async def test_workflow_execution_validation_error(
        self,
        mock_additional,
        mock_certs,
        mock_projects,
        mock_skills,
        mock_work,
        mock_education,
        mock_personal,
    ):
        """Test workflow execution with validation error."""
        # Setup mock personal info parser to return data without personal info
        from app.parser import PersonalInfoWithSummary

        personal_info = PersonalInfo()  # Empty personal info, no full_name
        combined_result = PersonalInfoWithSummary(personal_info=personal_info, summary=None)
        mock_personal_parser = Mock()
        mock_personal_parser.parse_from_text = AsyncMock(return_value=combined_result)
        mock_personal.return_value = mock_personal_parser

        # Setup other mock parsers
        for mock_parser_class in [mock_education, mock_work, mock_skills, mock_projects]:
            mock_parser = Mock()
            mock_parser.parse_from_text = AsyncMock(return_value=[])
            mock_parser_class.return_value = mock_parser

        # Special case for parsers that return dicts
        for mock_parser_class in [mock_certs, mock_additional]:
            mock_parser = Mock()
            mock_parser.parse_from_text = AsyncMock(return_value={})
            mock_parser_class.return_value = mock_parser

        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "errors" in result
        assert "Could not extract basic personal information" in result["errors"][0]


class TestParserEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_with_invalid_api_key(self, mock_chat_openai):
        """Test parser behavior with invalid API key."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Invalid API key")
        mock_llm.with_structured_output.return_value = mock_chain
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_with_timeout(self, mock_chat_openai):
        """Test parser behavior with timeout."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = TimeoutError("Request timeout")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_with_rate_limit(self, mock_chat_openai):
        """Test parser behavior with rate limiting."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Rate limit exceeded")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_with_very_long_text(self, mock_chat_openai):
        """Test parser with very long CV text."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        parsed_data = CVData(personal_info=PersonalInfo(full_name="John Doe"))
        mock_chain.ainvoke.return_value = parsed_data
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        # Very long text (100k characters)
        long_text = "A" * 100000

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text(long_text, "test.pdf")

        assert result.personal_info.full_name == "John Doe"
        mock_chain.ainvoke.assert_called_once_with(long_text)

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_with_special_characters(self, mock_chat_openai):
        """Test parser with special characters in text."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        parsed_data = CVData(personal_info=PersonalInfo(full_name="José María Martínez-González"))
        mock_chain.ainvoke.return_value = parsed_data
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        # Text with special characters
        special_text = "José María Martínez-González\n软件工程师\n🎓 教育背景"

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text(special_text, "test.pdf")

        assert result.personal_info.full_name == "José María Martínez-González"

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_with_empty_text(self, mock_chat_openai):
        """Test parser with empty text."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        # Parser might return empty data or error for empty text
        mock_chain.ainvoke.side_effect = Exception("No content to parse")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception):
                await parser.parse_from_text("", "test.pdf")

    @pytest.mark.asyncio
    @patch("app.parser.ChatOpenAI")
    async def test_parser_multimodal_with_invalid_base64(self, mock_chat_openai):
        """Test multimodal parser with invalid base64 content."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Invalid base64 content")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_multimodal_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_file("invalid_base64", "application/pdf", "test.pdf")

        assert "Error parsing CV with multimodal" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.CVParser")
    async def test_multimodal_single_api_call_optimization(self, mock_cv_parser_class):
        """Test that multimodal parsing makes only one API call instead of seven."""
        from app.parser import cv_parser_workflow

        # Mock the multimodal API response
        mock_cv_data = CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>"),
            summary="Experienced software engineer",
            education=[Education(school="MIT", degree="BS Computer Science")],
            work_experience=[WorkExperience(company="TechCorp", job_title="Senior Engineer")],
            skills=[Skill(name="Python", category="Programming")],
            projects=[Project(name="AI Assistant")],
            certifications=[Certification(name="AWS Certified")],
            awards=[Award(title="Employee of the Year")],
            languages=[Language(language="English", proficiency="Native")],
            publications=[],
            references=[],
            volunteer_experience=[],
            extracurricular_activities=[],
        )

        # Set up mock parser instance
        mock_parser_instance = Mock()
        mock_parser_instance.parse_from_file = AsyncMock(return_value=mock_cv_data)
        mock_cv_parser_class.return_value = mock_parser_instance

        # Set up initial state for multimodal parsing
        initial_state: CVParserState = {
            "input_text": None,
            "input_file_base64": "fake_base64_data",
            "filename": "test.pdf",
            "parsed_data": None,
            "errors": [],
            "mode": "multimodal",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        # Execute the workflow
        result = await cv_parser_workflow.ainvoke(initial_state)

        # Verify that parse_from_file was called only ONCE (not 7 times)
        # The call should be from multimodal_api_call_node, not from individual parsers
        mock_parser_instance.parse_from_file.assert_called_once()

        # Verify that no errors occurred
        assert "errors" not in result or not result["errors"], (
            f"Errors occurred: {result.get('errors', [])}"
        )

        # Verify that the result contains properly extracted data
        assert "parsed_data" in result
        assert result["parsed_data"] is not None, f"parsed_data is None, full result: {result}"
        assert result["parsed_data"].personal_info.full_name == "John Doe"
        assert result["parsed_data"].summary == "Experienced software engineer"
        assert len(result["parsed_data"].education) == 1
        assert len(result["parsed_data"].work_experience) == 1
        assert len(result["parsed_data"].skills) == 1
