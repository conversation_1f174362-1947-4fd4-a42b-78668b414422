#!/usr/bin/env python3
"""
Test script for Dify CV parser with mock callback.

This script demonstrates how to test the CV parsing with callback functionality.
"""

import asyncio
import sys
from pathlib import Path

import httpx


async def test_cv_parsing_with_callback(cv_file_path: str, callback_url: str = "http://localhost:8080/mock/callback"):
    """
    Test CV parsing with callback URL.
    
    Args:
        cv_file_path: Path to the CV file to test
        callback_url: Callback URL to receive results
    """
    
    # Check if file exists
    file_path = Path(cv_file_path)
    if not file_path.exists():
        print(f"❌ File not found: {cv_file_path}")
        return
    
    print(f"🚀 Testing CV parsing with callback...")
    print(f"📄 File: {cv_file_path}")
    print(f"🔗 Callback URL: {callback_url}")
    print("-" * 50)
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Prepare the request
            with open(file_path, "rb") as f:
                files = {"file": (file_path.name, f, "application/octet-stream")}
                data = {"callback_url": callback_url}
                
                # Send request to Dify parser
                print("📤 Sending CV to Dify parser...")
                response = await client.post(
                    "http://localhost:8080/v1/parse",
                    files=files,
                    data=data
                )
                
                response.raise_for_status()
                result = response.json()
                
                print("✅ Request sent successfully!")
                print(f"📋 Task ID: {result.get('task_id', 'N/A')}")
                print(f"🔄 Status: {result.get('status', 'N/A')}")
                
                if result.get("status") == "processing":
                    print("⏳ CV is being processed asynchronously...")
                    print("🔔 Check your server logs for callback notifications!")
                    print("\n💡 To see callback logs, check your FastAPI server console.")
                else:
                    print("📊 Synchronous processing completed:")
                    print(f"✅ Success: {result.get('success', False)}")
                    if result.get('profile'):
                        print(f"👤 Profile keys: {list(result['profile'].keys())}")
                
    except httpx.HTTPStatusError as e:
        print(f"❌ HTTP Error: {e.response.status_code}")
        print(f"📝 Response: {e.response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")


async def test_mock_callback_directly():
    """Test the mock callback endpoint directly."""
    
    print("🧪 Testing mock callback endpoint directly...")
    print("-" * 50)
    
    # Sample callback payload
    test_payload = {
        "task_id": "test-task-123",
        "workflow_run_id": "test-workflow-456",
        "status": "succeeded",
        "success": True,
        "profile": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "******-0123"
        },
        "meta_data": {
            "cv_text": "Sample CV text content...",
            "parser_time": "2025-06-17T10:30:00.000000"
        },
        "elapsed_time": 25.5,
        "total_tokens": 1500,
        "total_steps": 8,
        "created_at": 1718625000,
        "finished_at": 1718625025,
        "original_filename": "test-cv.pdf"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8080/mock/callback",
                json=test_payload
            )
            
            response.raise_for_status()
            result = response.json()
            
            print("✅ Mock callback test successful!")
            print(f"📋 Response: {result}")
            
    except Exception as e:
        print(f"❌ Error testing mock callback: {str(e)}")


def main():
    """Main function to run tests."""
    
    print("🔧 Dify CV Parser Callback Test")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("📖 Usage:")
        print("  python test_callback.py <cv_file_path> [callback_url]")
        print("\n📝 Examples:")
        print("  python test_callback.py sample.pdf")
        print("  python test_callback.py sample.pdf http://localhost:8080/mock/callback")
        print("  python test_callback.py --test-mock  # Test mock callback only")
        print("\n💡 Make sure your FastAPI server is running on http://localhost:8080")
        return
    
    if sys.argv[1] == "--test-mock":
        # Test mock callback directly
        asyncio.run(test_mock_callback_directly())
    else:
        # Test CV parsing with callback
        cv_file = sys.argv[1]
        callback_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8080/mock/callback"
        
        asyncio.run(test_cv_parsing_with_callback(cv_file, callback_url))


if __name__ == "__main__":
    main()
