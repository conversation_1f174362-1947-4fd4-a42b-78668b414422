import base64
import logging
import operator
from datetime import UTC, datetime
from typing import Annotated, Any, TypedDict

from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema import HumanMessage
from langchain.schema.runnable import RunnablePassthrough
from langchain_openai import Chat<PERSON>penA<PERSON>
from langgraph.graph import END, StateGraph
from pydantic import BaseModel

from app.config import settings
from app.models import (
    Award,
    Certification,
    CVData,
    Education,
    ExtracurricularActivity,
    Language,
    Metadata,
    PersonalInfo,
    Project,
    Publication,
    Reference,
    Skill,
    VolunteerExperience,
    WorkExperience,
)
from app.utils import normalize_phone_number

# Set up logger for OpenAI API calls
logger = logging.getLogger(__name__)


# Helper model for PersonalInfoParser to extract both personal info and summary
class PersonalInfoWithSummary(BaseModel):
    """Combined model for personal info and summary extraction"""

    personal_info: PersonalInfo
    summary: str | None = None


# Define state for LangGraph
class CVParserState(TypedDict):
    """State for CV parsing workflow"""

    input_text: str | None
    input_file_base64: str | None
    filename: str
    parsed_data: CVData | None
    errors: Annotated[list[str], operator.add]  # Collect errors from parallel nodes
    mode: str  # "text" or "multimodal"
    multimodal_full_result: CVData | None  # Stores complete result from single multimodal API call

    # Parallel parsing results
    personal_info_result: PersonalInfo | None
    summary_result: str | None  # Summary extracted by PersonalInfoParser
    education_result: list[Education] | None
    work_experience_result: list[WorkExperience] | None
    skills_result: list[Skill] | None
    projects_result: list[Project] | None
    certifications_result: list[Certification] | None
    additional_sections_result: dict[str, Any] | None


class CVParser:
    """CV Parser using LangChain with structured output"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        # Create prompt template for text-based parsing
        self.text_prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert CV/Resume parser. Extract information from the provided CV text and structure it according to the given schema.

Guidelines:
- Extract all relevant information accurately
- If information is not available, leave the field as null
- Parse dates in a consistent format when possible
- Categorize skills appropriately
- Extract complete descriptions for work experience and projects
- Be thorough but accurate - don't invent information""",
                ),
                ("human", "Parse the following CV/Resume text:\n\n{cv_text}"),
            ]
        )

        # Create prompt template for multimodal parsing
        self.multimodal_prompt_text = """You are an expert CV/Resume parser. Analyze the provided document image and extract information according to the given schema.

Guidelines:
- Extract all relevant information accurately from the visual document
- If information is not available, leave the field as null
- Parse dates in a consistent format when possible
- Categorize skills appropriately
- Extract complete descriptions for work experience and projects
- Be thorough but accurate - don't invent information

Analyze the attached CV/Resume document and extract all information."""

    def create_text_chain(self):
        """Create chain for text-based parsing"""
        return (
            {"cv_text": RunnablePassthrough()}
            | self.text_prompt
            | self.llm.with_structured_output(CVData)
        )

    def create_multimodal_chain(self):
        """Create chain for multimodal parsing"""
        return self.llm.with_structured_output(CVData)

    async def parse_from_text(self, text: str, filename: str) -> CVData:
        """Parse CV from extracted text"""
        try:
            logger.info(f"Starting text-based CV parsing for file: {filename}")
            logger.debug(f"Text length: {len(text)} characters")

            chain = self.create_text_chain()
            parsed_data = await chain.ainvoke(text)

            logger.info(f"Successfully parsed CV text for file: {filename}")

            # Add metadata
            parsed_data.metadata = Metadata(
                parsed_at=datetime.now(UTC).isoformat(),
                parser_version=settings.PARSER_VERSION,
                source_file_name=filename,
            )

            return parsed_data
        except Exception as e:
            logger.error(f"Error parsing CV text for file {filename}: {str(e)}", exc_info=True)
            raise Exception(f"Error parsing CV text: {str(e)}")

    async def parse_from_file(self, file_base64: str, mime_type: str, filename: str) -> CVData:
        """Parse CV directly from file using multimodal capabilities"""
        try:
            logger.info(f"Starting multimodal CV parsing for file: {filename}")
            logger.debug(f"MIME type: {mime_type}, base64 length: {len(file_base64)} characters")

            # Handle PDF files - convert to images first
            if mime_type == "application/pdf":
                from app.file_processors import PDFToImageConverter

                logger.info(f"Converting PDF to images for multimodal processing: {filename}")

                # Decode base64 to bytes
                pdf_bytes = base64.b64decode(file_base64)

                # Convert PDF to images
                image_base64_list = PDFToImageConverter.convert_pdf_to_images(pdf_bytes, filename)

                # Create message content with text prompt and all images
                content = [{"type": "text", "text": self.multimodal_prompt_text}]

                # Add each image to the content
                image_format = settings.PDF_IMAGE_FORMAT.lower()
                mime_type_image = f"image/{image_format}"

                for i, img_base64 in enumerate(image_base64_list):
                    content.append(
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:{mime_type_image};base64,{img_base64}"},
                        }
                    )
                    logger.debug(f"Added page {i + 1} to multimodal request")

                logger.info(
                    f"Sending {len(image_base64_list)} images to OpenAI Vision API for {filename}"
                )

            else:
                # Non-PDF files - send directly (for future support of other image formats)
                content = [
                    {"type": "text", "text": self.multimodal_prompt_text},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime_type};base64,{file_base64}"},
                    },
                ]
                logger.info(
                    f"Sending {mime_type} file directly to OpenAI Vision API for {filename}"
                )

            # Create message with content
            messages = [HumanMessage(content=content)]

            logger.debug("Sending request to OpenAI Vision API")
            chain = self.create_multimodal_chain()
            parsed_data = await chain.ainvoke(messages)

            logger.info(f"Successfully parsed CV with multimodal for file: {filename}")

            # Add metadata
            parsed_data.metadata = Metadata(
                parsed_at=datetime.now(UTC).isoformat(),
                parser_version=settings.PARSER_VERSION,
                source_file_name=filename,
            )

            return parsed_data
        except Exception as e:
            logger.error(
                f"Error parsing CV with multimodal for file {filename}: {str(e)}", exc_info=True
            )
            # Log specific OpenAI API errors
            if "400" in str(e):
                logger.error(
                    f"OpenAI API 400 Bad Request for {filename} - possible invalid base64 or rate limit"
                )
            elif "429" in str(e):
                logger.error(f"OpenAI API 429 Rate Limit exceeded for {filename}")
            elif "401" in str(e):
                logger.error(f"OpenAI API 401 Unauthorized for {filename} - check API key")
            elif "500" in str(e):
                logger.error(f"OpenAI API 500 Internal Server Error for {filename}")

            raise Exception(f"Error parsing CV with multimodal: {str(e)}")


# Specialized Section Parsers for Parallel Processing
class PersonalInfoParser:
    """Parser specialized for personal information section"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting personal information and professional summary from CV/Resume text.

Extract personal contact information, basic details, AND professional summary:

Personal Information:
- Full name
- Email address
- Phone number (format as +84-xxxxxxxxx for Vietnamese numbers, or keep original format for international numbers)
- Location/Address
- LinkedIn URL
- GitHub URL
- Personal website
- Date of birth (if mentioned)
- Nationality (if mentioned)

Professional Summary:
- Extract any professional summary, objective, or career summary section
- This may appear as "Summary", "Professional Summary", "Career Objective", "About Me", etc.
- If no dedicated summary section exists, extract a brief overview of their professional background from the opening paragraph or profile section
- Keep it concise and professional

Guidelines:
- Extract only what is explicitly mentioned
- Don't invent or assume information
- Return null for missing fields
- Be precise with contact details
- For phone numbers: If the number appears to be Vietnamese (starts with 0, +84, or 84), format it as +84-xxxxxxxxx (remove leading 0 if present, add +84- prefix)
- For summary, capture the essence of their professional profile""",
                ),
                (
                    "human",
                    "Extract personal information and summary from this CV text:\n\n{cv_text}",
                ),
            ]
        )

    def create_chain(self):
        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(PersonalInfoWithSummary)
        )

    async def parse_from_text(self, text: str) -> PersonalInfoWithSummary:
        try:
            logger.debug("PersonalInfoParser: Starting personal info and summary extraction")
            chain = self.create_chain()
            result = await chain.ainvoke(text)

            # Normalize phone number format
            if result.personal_info and result.personal_info.phone:
                result.personal_info.phone = normalize_phone_number(result.personal_info.phone)
                logger.debug(
                    f"PersonalInfoParser: Normalized phone number to: {result.personal_info.phone}"
                )

            logger.debug("PersonalInfoParser: Successfully extracted personal info and summary")
            return result
        except Exception as e:
            logger.error(
                f"PersonalInfoParser: Error extracting personal info and summary: {str(e)}",
                exc_info=True,
            )
            raise


class EducationParser:
    """Parser specialized for education section"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting education information from CV/Resume text.
            
Extract ONLY education-related information:
- Schools/Universities attended
- Degrees obtained
- Fields of study
- Start and end dates
- GPA (if mentioned)
- Relevant coursework or descriptions

Guidelines:
- List education in reverse chronological order
- Parse dates consistently (YYYY-MM format preferred)
- Include all educational institutions
- Extract complete degree names""",
                ),
                ("human", "Extract education information from this CV text:\n\n{cv_text}"),
            ]
        )

    def create_chain(self):
        from pydantic import BaseModel

        class EducationList(BaseModel):
            education: list[Education]

        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(EducationList)
        )

    async def parse_from_text(self, text: str) -> list[Education]:
        try:
            logger.debug("EducationParser: Starting education extraction")
            chain = self.create_chain()
            result = await chain.ainvoke(text)
            logger.debug(
                f"EducationParser: Successfully extracted {len(result.education)} education entries"
            )
            return result.education
        except Exception as e:
            logger.error(f"EducationParser: Error extracting education: {str(e)}", exc_info=True)
            raise


class WorkExperienceParser:
    """Parser specialized for work experience section"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting work experience from CV/Resume text.
            
Extract ONLY work experience information:
- Company names
- Job titles/positions
- Employment type (Full-time, Part-time, Contract, etc.)
- Locations
- Start and end dates
- Job descriptions and responsibilities

Guidelines:
- List positions in reverse chronological order
- Parse dates consistently (YYYY-MM format preferred)
- Extract complete job descriptions
- Include all relevant work experience""",
                ),
                ("human", "Extract work experience from this CV text:\n\n{cv_text}"),
            ]
        )

    def create_chain(self):
        from pydantic import BaseModel

        class WorkExperienceList(BaseModel):
            work_experience: list[WorkExperience]

        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(WorkExperienceList)
        )

    async def parse_from_text(self, text: str) -> list[WorkExperience]:
        try:
            logger.debug("WorkExperienceParser: Starting work experience extraction")
            chain = self.create_chain()
            result = await chain.ainvoke(text)
            logger.debug(
                f"WorkExperienceParser: Successfully extracted {len(result.work_experience)} work experience entries"
            )
            return result.work_experience
        except Exception as e:
            logger.error(
                f"WorkExperienceParser: Error extracting work experience: {str(e)}", exc_info=True
            )
            raise


class SkillsParser:
    """Parser specialized for skills section"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting skills from CV/Resume text.
            
Extract ONLY skills and categorize them appropriately:
- Programming languages
- Frameworks and libraries
- Tools and software
- Technical skills
- Soft skills
- Certifications (technical)

Guidelines:
- Categorize skills logically (e.g., "Programming Languages", "Frameworks", "Tools", "Soft Skills")
- Extract both explicitly listed skills and skills mentioned in experience descriptions
- Don't duplicate skills across categories
- Be comprehensive but accurate""",
                ),
                ("human", "Extract skills from this CV text:\n\n{cv_text}"),
            ]
        )

    def create_chain(self):
        from pydantic import BaseModel

        class SkillsList(BaseModel):
            skills: list[Skill]

        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(SkillsList)
        )

    async def parse_from_text(self, text: str) -> list[Skill]:
        try:
            logger.debug("SkillsParser: Starting skills extraction")
            chain = self.create_chain()
            result = await chain.ainvoke(text)
            logger.debug(f"SkillsParser: Successfully extracted {len(result.skills)} skills")
            return result.skills
        except Exception as e:
            logger.error(f"SkillsParser: Error extracting skills: {str(e)}", exc_info=True)
            raise


class ProjectsParser:
    """Parser specialized for projects section"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting project information from CV/Resume text.
            
Extract ONLY project-related information:
- Project names
- Descriptions
- Your role in the project
- Technologies used
- Start and end dates
- Project URLs (if mentioned)

Guidelines:
- Include both personal and professional projects
- Extract complete descriptions
- List technologies used for each project
- Parse dates consistently""",
                ),
                ("human", "Extract project information from this CV text:\n\n{cv_text}"),
            ]
        )

    def create_chain(self):
        from pydantic import BaseModel

        class ProjectsList(BaseModel):
            projects: list[Project]

        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(ProjectsList)
        )

    async def parse_from_text(self, text: str) -> list[Project]:
        try:
            logger.debug("ProjectsParser: Starting projects extraction")
            chain = self.create_chain()
            result = await chain.ainvoke(text)
            logger.debug(f"ProjectsParser: Successfully extracted {len(result.projects)} projects")
            return result.projects
        except Exception as e:
            logger.error(f"ProjectsParser: Error extracting projects: {str(e)}", exc_info=True)
            raise


class CertificationsParser:
    """Parser specialized for certifications, awards, and publications"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting certifications, awards, and publications from CV/Resume text.
            
Extract information for:
- Professional certifications
- Awards and honors
- Publications

Guidelines:
- Include issuing organizations
- Extract dates (issue dates, expiration dates)
- Include credential IDs if mentioned
- Be precise with certification names""",
                ),
                (
                    "human",
                    "Extract certifications, awards, and publications from this CV text:\n\n{cv_text}",
                ),
            ]
        )

    def create_chain(self):
        from pydantic import BaseModel

        class CertificationAwards(BaseModel):
            certifications: list[Certification]
            awards: list[Award]
            publications: list[Publication]

        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(CertificationAwards)
        )

    async def parse_from_text(self, text: str) -> dict[str, Any]:
        try:
            logger.debug(
                "CertificationsParser: Starting certifications, awards, and publications extraction"
            )
            chain = self.create_chain()
            result = await chain.ainvoke(text)
            logger.debug(
                f"CertificationsParser: Successfully extracted {len(result.certifications)} certifications, "
                f"{len(result.awards)} awards, {len(result.publications)} publications"
            )
            return {
                "certifications": result.certifications,
                "awards": result.awards,
                "publications": result.publications,
            }
        except Exception as e:
            logger.error(
                f"CertificationsParser: Error extracting certifications/awards/publications: {str(e)}",
                exc_info=True,
            )
            raise


class AdditionalSectionsParser:
    """Parser specialized for additional sections like languages, volunteer work, etc."""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert at extracting additional information from CV/Resume text.
            
Extract information for:
- Languages and proficiency levels
- Volunteer experience
- Extracurricular activities
- References

Guidelines:
- For languages, include proficiency levels
- For volunteer work, include organizations and roles
- For activities, include positions held
- Extract all relevant additional information""",
                ),
                (
                    "human",
                    "Extract additional sections (languages, volunteer work, activities, references) from this CV text:\n\n{cv_text}",
                ),
            ]
        )

    def create_chain(self):
        from pydantic import BaseModel

        class AdditionalSections(BaseModel):
            languages: list[Language]
            volunteer_experience: list[VolunteerExperience]
            extracurricular_activities: list[ExtracurricularActivity]
            references: list[Reference]

        return (
            {"cv_text": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(AdditionalSections)
        )

    async def parse_from_text(self, text: str) -> dict[str, Any]:
        try:
            logger.debug("AdditionalSectionsParser: Starting additional sections extraction")
            chain = self.create_chain()
            result = await chain.ainvoke(text)
            logger.debug(
                f"AdditionalSectionsParser: Successfully extracted {len(result.languages)} languages, "
                f"{len(result.volunteer_experience)} volunteer experiences, "
                f"{len(result.extracurricular_activities)} activities, {len(result.references)} references"
            )
            return {
                "languages": result.languages,
                "volunteer_experience": result.volunteer_experience,
                "extracurricular_activities": result.extracurricular_activities,
                "references": result.references,
            }
        except Exception as e:
            logger.error(
                f"AdditionalSectionsParser: Error extracting additional sections: {str(e)}",
                exc_info=True,
            )
            raise


# LangGraph workflow nodes
async def extract_text_node(state: CVParserState) -> dict[str, Any]:
    """Node for text extraction (already done in API layer)"""
    # Text extraction is handled in the API layer
    # This node just validates the state
    if state["mode"] == "text" and not state.get("input_text"):
        return {"errors": ["No text provided for text mode parsing"]}
    elif state["mode"] == "multimodal" and not state.get("input_file_base64"):
        return {"errors": ["No file data provided for multimodal parsing"]}
    return {}


async def multimodal_api_call_node(state: CVParserState) -> dict[str, Any]:
    """Node for making the single multimodal API call and storing the result"""
    if state.get("errors"):
        return {}

    if state["mode"] != "multimodal":
        logger.debug("Skipping multimodal API call - mode is text")
        return {}  # Skip this node for text mode

    try:
        logger.info(f"Making multimodal API call for file: {state['filename']}")
        cv_parser = CVParser()
        mime_type = "application/pdf"
        full_data = await cv_parser.parse_from_file(
            state["input_file_base64"], mime_type, state["filename"]
        )
        logger.info(f"Multimodal API call successful for file: {state['filename']}")
        return {"multimodal_full_result": full_data}
    except Exception as e:
        logger.error(
            f"Multimodal API call failed for file {state['filename']}: {str(e)}", exc_info=True
        )
        return {"errors": [f"Multimodal API call failed: {str(e)}"]}


# Parallel parsing nodes for different CV sections
async def parse_personal_info_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing personal information section"""
    if state.get("errors"):
        return {}

    try:
        if state["mode"] == "text":
            parser = PersonalInfoParser()
            combined_result = await parser.parse_from_text(state["input_text"])
            return {
                "personal_info_result": combined_result.personal_info,
                "summary_result": combined_result.summary,
            }
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                # Normalize phone number for multimodal results too
                personal_info = multimodal_result.personal_info
                if personal_info and personal_info.phone:
                    personal_info.phone = normalize_phone_number(personal_info.phone)
                    logger.debug(f"Normalized multimodal phone number to: {personal_info.phone}")

                return {
                    "personal_info_result": personal_info,
                    "summary_result": multimodal_result.summary,
                }
            else:
                return {"errors": ["No multimodal result available for personal info parsing"]}
    except Exception as e:
        return {"errors": [f"Personal info parsing failed: {str(e)}"]}


async def parse_education_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing education section"""
    if state.get("errors"):
        return {}

    try:
        parser = EducationParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = multimodal_result.education
            else:
                return {"errors": ["No multimodal result available for education parsing"]}

        return {"education_result": result}
    except Exception as e:
        return {"errors": [f"Education parsing failed: {str(e)}"]}


async def parse_work_experience_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing work experience section"""
    if state.get("errors"):
        return {}

    try:
        parser = WorkExperienceParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = multimodal_result.work_experience
            else:
                return {"errors": ["No multimodal result available for work experience parsing"]}

        return {"work_experience_result": result}
    except Exception as e:
        return {"errors": [f"Work experience parsing failed: {str(e)}"]}


async def parse_skills_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing skills section"""
    if state.get("errors"):
        return {}

    try:
        parser = SkillsParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = multimodal_result.skills
            else:
                return {"errors": ["No multimodal result available for skills parsing"]}

        return {"skills_result": result}
    except Exception as e:
        return {"errors": [f"Skills parsing failed: {str(e)}"]}


async def parse_projects_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing projects section"""
    if state.get("errors"):
        return {}

    try:
        parser = ProjectsParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = multimodal_result.projects
            else:
                return {"errors": ["No multimodal result available for projects parsing"]}

        return {"projects_result": result}
    except Exception as e:
        return {"errors": [f"Projects parsing failed: {str(e)}"]}


async def parse_certifications_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing certifications, awards, and publications"""
    if state.get("errors"):
        return {}

    try:
        parser = CertificationsParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = {
                    "certifications": multimodal_result.certifications,
                    "awards": multimodal_result.awards,
                    "publications": multimodal_result.publications,
                }
            else:
                return {"errors": ["No multimodal result available for certifications parsing"]}

        return {"certifications_result": result}
    except Exception as e:
        return {"errors": [f"Certifications parsing failed: {str(e)}"]}


async def parse_additional_sections_node(state: CVParserState) -> dict[str, Any]:
    """Node for parsing additional sections (languages, volunteer work, etc.)"""
    if state.get("errors"):
        return {}

    try:
        parser = AdditionalSectionsParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = {
                    "languages": multimodal_result.languages,
                    "volunteer_experience": multimodal_result.volunteer_experience,
                    "extracurricular_activities": multimodal_result.extracurricular_activities,
                    "references": multimodal_result.references,
                }
            else:
                return {
                    "errors": ["No multimodal result available for additional sections parsing"]
                }

        return {"additional_sections_result": result}
    except Exception as e:
        return {"errors": [f"Additional sections parsing failed: {str(e)}"]}


async def aggregate_results_node(state: CVParserState) -> dict[str, Any]:
    """Node for aggregating all parallel parsing results into final CVData"""
    if state.get("errors"):
        return {
            "errors": [f"Cannot aggregate due to previous errors: {'; '.join(state['errors'])}"]
        }

    try:
        # Extract all parsed sections
        personal_info = state.get("personal_info_result")
        summary = state.get("summary_result")  # Summary extracted by PersonalInfoParser
        education = state.get("education_result") or []
        work_experience = state.get("work_experience_result") or []
        skills = state.get("skills_result") or []
        projects = state.get("projects_result") or []

        # Extract certifications results (includes certifications, awards, publications)
        cert_results = state.get("certifications_result") or {}
        certifications = cert_results.get("certifications", [])
        awards = cert_results.get("awards", [])
        publications = cert_results.get("publications", [])

        # Extract additional sections results
        additional_results = state.get("additional_sections_result") or {}
        languages = additional_results.get("languages", [])
        volunteer_experience = additional_results.get("volunteer_experience", [])
        extracurricular_activities = additional_results.get("extracurricular_activities", [])
        references = additional_results.get("references", [])

        # Create metadata
        metadata = Metadata(
            parsed_at=datetime.now(UTC).isoformat(),
            parser_version=settings.PARSER_VERSION,
            source_file_name=state["filename"],
        )

        # Aggregate into final CVData object
        parsed_data = CVData(
            personal_info=personal_info,
            summary=summary,  # Summary extracted by PersonalInfoParser
            education=education,
            work_experience=work_experience,
            skills=skills,
            projects=projects,
            certifications=certifications,
            awards=awards,
            languages=languages,
            publications=publications,
            references=references,
            volunteer_experience=volunteer_experience,
            extracurricular_activities=extracurricular_activities,
            metadata=metadata,
        )

        return {"parsed_data": parsed_data}
    except Exception as e:
        return {"errors": [f"Results aggregation failed: {str(e)}"]}


def validation_node(state: CVParserState) -> dict[str, Any]:
    """Node for validating parsed data"""
    if state.get("errors"):
        return {}

    parsed_data = state.get("parsed_data")
    if not parsed_data:
        return {"errors": ["No parsed data available"]}

    # Basic validation - ensure at least some key information was extracted
    if not parsed_data.personal_info or not parsed_data.personal_info.full_name:
        return {"errors": ["Could not extract basic personal information from CV"]}

    return {}


# Create LangGraph workflow
def create_cv_parser_workflow() -> StateGraph:
    """Create the CV parsing workflow using LangGraph with parallel section parsing"""
    workflow = StateGraph(CVParserState)

    # Add nodes
    workflow.add_node("extract_text", extract_text_node)
    workflow.add_node("multimodal_api_call", multimodal_api_call_node)

    # Parallel parsing nodes
    workflow.add_node("parse_personal_info", parse_personal_info_node)
    workflow.add_node("parse_education", parse_education_node)
    workflow.add_node("parse_work_experience", parse_work_experience_node)
    workflow.add_node("parse_skills", parse_skills_node)
    workflow.add_node("parse_projects", parse_projects_node)
    workflow.add_node("parse_certifications", parse_certifications_node)
    workflow.add_node("parse_additional_sections", parse_additional_sections_node)

    # Aggregation and validation nodes
    workflow.add_node("aggregate_results", aggregate_results_node)
    workflow.add_node("validate", validation_node)

    # Route to multimodal API call first, then to parsers
    # multimodal_api_call_node will only act if mode is multimodal
    workflow.add_edge("extract_text", "multimodal_api_call")

    # After multimodal API call (or skip), go to parallel parsers
    workflow.add_edge("multimodal_api_call", "parse_personal_info")
    workflow.add_edge("multimodal_api_call", "parse_education")
    workflow.add_edge("multimodal_api_call", "parse_work_experience")
    workflow.add_edge("multimodal_api_call", "parse_skills")
    workflow.add_edge("multimodal_api_call", "parse_projects")
    workflow.add_edge("multimodal_api_call", "parse_certifications")
    workflow.add_edge("multimodal_api_call", "parse_additional_sections")

    # All parallel nodes feed into aggregation
    workflow.add_edge("parse_personal_info", "aggregate_results")
    workflow.add_edge("parse_education", "aggregate_results")
    workflow.add_edge("parse_work_experience", "aggregate_results")
    workflow.add_edge("parse_skills", "aggregate_results")
    workflow.add_edge("parse_projects", "aggregate_results")
    workflow.add_edge("parse_certifications", "aggregate_results")
    workflow.add_edge("parse_additional_sections", "aggregate_results")

    # Final validation and end
    workflow.add_edge("aggregate_results", "validate")
    workflow.add_edge("validate", END)

    # Set entry point
    workflow.set_entry_point("extract_text")

    return workflow.compile()


# Create singleton instance of the workflow
cv_parser_workflow = create_cv_parser_workflow()
