import base64
import io
import logging
import os
import subprocess
import tempfile
from pathlib import Path

import docx2txt
import pdftotext
from fastapi import HTTPEx<PERSON>, UploadFile
from pdf2image import convert_from_bytes

from app.config import settings

# Set up logger
logger = logging.getLogger(__name__)


class FileProcessor:
    """Base class for file processing"""

    @staticmethod
    def validate_file(file: UploadFile, allowed_extensions: set, max_size: int) -> None:
        """Validate uploaded file"""
        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_ext} not supported. Allowed types: {', '.join(allowed_extensions)}",
            )

        # Check file size
        file.file.seek(0, 2)  # Move to end of file
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning

        if file_size > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum allowed size of {max_size / (1024 * 1024):.1f}MB",
            )

    @staticmethod
    async def save_temp_file(file: UploadFile) -> str:
        """Save uploaded file to temporary location"""
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=Path(file.filename).suffix
        ) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            return tmp_file.name


class PDFProcessor(FileProcessor):
    """Process PDF files using pdftotext"""

    @staticmethod
    def extract_text(file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            with open(file_path, "rb") as f:
                pdf = pdftotext.PDF(f)

            # Join all pages with newlines
            text = "\n\n".join(pdf)

            if not text.strip():
                raise ValueError("No text content found in PDF")

            return text
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Error processing PDF: {str(e)}")


class DOCProcessor(FileProcessor):
    """Process DOC files using antiword"""

    @staticmethod
    def extract_text(file_path: str) -> str:
        """Extract text from DOC file using antiword"""
        try:
            # Check if antiword is installed
            result = subprocess.run(["which", "antiword"], capture_output=True, text=True)

            if result.returncode != 0:
                raise HTTPException(
                    status_code=500,
                    detail="antiword is not installed. Please install it using: brew install antiword (macOS) or apt-get install antiword (Linux)",
                )

            # Extract text using antiword
            result = subprocess.run(
                ["antiword", file_path], capture_output=True, text=True, check=True
            )

            text = result.stdout

            if not text.strip():
                raise ValueError("No text content found in DOC file")

            return text
        except subprocess.CalledProcessError as e:
            raise HTTPException(status_code=400, detail=f"Error processing DOC file: {e.stderr}")
        except HTTPException:
            # Re-raise HTTPExceptions (like the 500 for missing antiword) without modification
            raise
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Error processing DOC file: {str(e)}")


class DOCXProcessor(FileProcessor):
    """Process DOCX files using python-docx2txt"""

    @staticmethod
    def extract_text(file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            text = docx2txt.process(file_path)

            if not text.strip():
                raise ValueError("No text content found in DOCX file")

            return text
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Error processing DOCX file: {str(e)}")


class PDFToImageConverter:
    """Converter for PDF files to images for multimodal processing"""

    @staticmethod
    def convert_pdf_to_images(pdf_bytes: bytes, filename: str = "document.pdf") -> list[str]:
        """
        Convert PDF bytes to list of base64-encoded images

        Args:
            pdf_bytes: PDF file content as bytes
            filename: Original filename for logging

        Returns:
            List of base64-encoded image strings (PNG format)

        Raises:
            HTTPException: If PDF conversion fails
        """
        try:
            logger.info(f"Converting PDF to images: {filename}")
            logger.debug(
                f"PDF size: {len(pdf_bytes)} bytes, DPI: {settings.PDF_TO_IMAGE_DPI}, Max pages: {settings.PDF_MAX_PAGES}"
            )

            # Convert PDF bytes to PIL Images
            images = convert_from_bytes(
                pdf_bytes,
                dpi=settings.PDF_TO_IMAGE_DPI,
                first_page=1,
                last_page=settings.PDF_MAX_PAGES,  # Limit pages to prevent excessive API costs
                fmt=settings.PDF_IMAGE_FORMAT.lower(),
            )

            if not images:
                raise ValueError("No pages found in PDF")

            logger.info(f"Successfully converted PDF to {len(images)} images: {filename}")

            # Convert images to base64 strings
            base64_images = []
            for i, image in enumerate(images):
                try:
                    # Convert PIL Image to base64
                    img_buffer = io.BytesIO()

                    # Save as PNG for better quality or JPEG for smaller size
                    image_format = settings.PDF_IMAGE_FORMAT.upper()
                    if image_format not in ["PNG", "JPEG"]:
                        image_format = "PNG"  # Default fallback

                    image.save(
                        img_buffer,
                        format=image_format,
                        quality=90 if image_format == "JPEG" else None,
                    )
                    img_buffer.seek(0)

                    # Encode to base64
                    img_base64 = base64.b64encode(img_buffer.getvalue()).decode("utf-8")
                    base64_images.append(img_base64)

                    logger.debug(
                        f"Converted page {i + 1}/{len(images)} to base64, size: {len(img_base64)} chars"
                    )

                except Exception as e:
                    logger.error(f"Error converting page {i + 1} to base64: {str(e)}")
                    raise ValueError(f"Failed to convert page {i + 1} to image")

            logger.info(
                f"Successfully converted {len(base64_images)} pages to base64 images: {filename}"
            )
            return base64_images

        except Exception as e:
            logger.error(f"Error converting PDF to images for {filename}: {str(e)}", exc_info=True)

            # Provide specific error messages for common issues
            error_msg = str(e)
            if "poppler" in error_msg.lower():
                raise HTTPException(
                    status_code=500,
                    detail="PDF conversion requires poppler-utils. Please install it using: brew install poppler (macOS) or apt-get install poppler-utils (Linux)",
                )
            elif "corrupted" in error_msg.lower() or "invalid" in error_msg.lower():
                raise HTTPException(
                    status_code=400, detail=f"Invalid or corrupted PDF file: {filename}"
                )
            elif "password" in error_msg.lower() or "encrypted" in error_msg.lower():
                raise HTTPException(
                    status_code=400,
                    detail=f"Password-protected PDF files are not supported: {filename}",
                )
            else:
                raise HTTPException(
                    status_code=500, detail=f"Error converting PDF to images: {error_msg}"
                )


class FileProcessorFactory:
    """Factory to get appropriate processor based on file type"""

    processors = {".pdf": PDFProcessor, ".doc": DOCProcessor, ".docx": DOCXProcessor}

    @classmethod
    def get_processor(cls, file_extension: str) -> FileProcessor:
        """Get processor for given file extension"""
        processor_class = cls.processors.get(file_extension.lower())
        if not processor_class:
            raise HTTPException(
                status_code=400, detail=f"No processor available for file type: {file_extension}"
            )
        return processor_class()


async def process_file(file: UploadFile, allowed_extensions: set, max_size: int) -> tuple[str, str]:
    """
    Process uploaded file and extract text
    Returns: (extracted_text, original_filename)
    """
    # Validate file
    FileProcessor.validate_file(file, allowed_extensions, max_size)

    # Save to temporary file
    temp_path = await FileProcessor.save_temp_file(file)

    try:
        # Get appropriate processor
        file_ext = Path(file.filename).suffix.lower()
        processor = FileProcessorFactory.get_processor(file_ext)

        # Extract text
        text = processor.extract_text(temp_path)

        return text, file.filename
    finally:
        # Clean up temporary file
        if os.path.exists(temp_path):
            os.unlink(temp_path)


async def get_file_base64(file: UploadFile) -> tuple[str, str]:
    """
    Convert file to base64 for multimodal processing
    Returns: (base64_content, mime_type)
    """
    content = await file.read()
    await file.seek(0)  # Reset file pointer

    # Determine MIME type
    file_ext = Path(file.filename).suffix.lower()
    mime_types = {
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    }

    mime_type = mime_types.get(file_ext, "application/octet-stream")
    base64_content = base64.b64encode(content).decode("utf-8")

    return base64_content, mime_type
