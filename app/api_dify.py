from pathlib import Path
from typing import Any, Dict, Optional

import httpx


async def upload_file_to_dify(
    file_path: str | Path,
    api_token: str,
    base_url: str = "https://dify.xstaging.navigosgroup.site",
    timeout: float = 30.0,
) -> Dict[str, Any]:
    """
    Upload a file to Dify API.

    Args:
        file_path: Path to the file to upload
        api_token: Dify API token (Bearer token)
        base_url: Base URL for the Dify API
        timeout: Request timeout in seconds

    Returns:
        Dict containing the API response with file information

    Raises:
        httpx.HTTPStatusError: If the API request fails
        FileNotFoundError: If the file doesn't exist
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")

    url = f"{base_url}/v1/files/upload"
    headers = {"Authorization": f"Bearer {api_token}"}

    async with httpx.AsyncClient(timeout=timeout) as client:
        with open(file_path, "rb") as file:
            files = {"file": (file_path.name, file, "application/octet-stream")}

            response = await client.post(url=url, headers=headers, files=files)

            response.raise_for_status()
            return response.json()
