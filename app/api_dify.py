import json
import logging
import tempfile
from pathlib import Path

import httpx
from fastapi import APIRouter, BackgroundTasks, File, Form, HTTPException, UploadFile
from pydantic import BaseModel, HttpUrl

from app.config import settings


async def upload_file_to_dify(
    file_path: str | Path,
    api_token: str | None = None,
    base_url: str | None = None,
    timeout: float = 30.0,
) -> dict[str, any]:
    """
    Upload a file to Dify API.

    Args:
        file_path: Path to the file to upload
        api_token: Dify API token (Bearer token). If None, uses settings.DIFY_API_TOKEN
        base_url: Base URL for the Dify API. If None, uses settings.DIFY_BASE_URL
        timeout: Request timeout in seconds

    Returns:
        Dict containing the API response with file information

    Raises:
        httpx.HTTPStatusError: If the API request fails
        FileNotFoundError: If the file doesn't exist
        ValueError: If api_token is not provided and not set in config
    """
    # Use config defaults if not provided
    if api_token is None:
        api_token = settings.DIFY_API_TOKEN
    if base_url is None:
        base_url = settings.DIFY_BASE_URL

    if not api_token:
        raise ValueError("Dify API token is required. Set DIFY_API_TOKEN in environment or pass api_token parameter.")

    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")

    url = f"{base_url}/v1/files/upload"
    headers = {"Authorization": f"Bearer {api_token}"}

    async with httpx.AsyncClient(timeout=timeout) as client:
        with open(file_path, "rb") as file:
            files = {"file": (file_path.name, file, "application/octet-stream")}

            response = await client.post(url=url, headers=headers, files=files)

            response.raise_for_status()
            return response.json()


async def start_cv_parse_streaming(
    upload_file_id: str,
    api_token: str | None = None,
    user: str = "campaign_hiring",
    base_url: str | None = None,
    timeout: float = 60.0,
) -> tuple[str, str]:
    """
    Start CV parsing with streaming mode and extract workflow_run_id and task_id from first message.

    Args:
        upload_file_id: ID of the uploaded file from upload_file_to_dify
        api_token: Dify API token (Bearer token). If None, uses settings.DIFY_API_TOKEN
        user: User identifier for the request
        base_url: Base URL for the Dify API. If None, uses settings.DIFY_BASE_URL
        timeout: Request timeout in seconds

    Returns:
        Tuple of (workflow_run_id, task_id) extracted from first streaming message

    Raises:
        httpx.HTTPStatusError: If the API request fails
        ValueError: If api_token is not provided and not set in config, or if IDs not found
    """
    # Use config defaults if not provided
    if api_token is None:
        api_token = settings.DIFY_API_TOKEN
    if base_url is None:
        base_url = settings.DIFY_BASE_URL

    if not api_token:
        raise ValueError("Dify API token is required. Set DIFY_API_TOKEN in environment or pass api_token parameter.")

    url = f"{base_url}/v1/workflows/run"
    headers = {"Authorization": f"Bearer {api_token}", "Content-Type": "application/json"}

    payload = {
        "inputs": {"cv_file": {"transfer_method": "local_file", "upload_file_id": upload_file_id, "type": "document"}},
        "response_mode": "streaming",
        "user": user,
    }

    async with httpx.AsyncClient(timeout=timeout) as client:
        async with client.stream("POST", url=url, headers=headers, json=payload) as response:
            response.raise_for_status()

            # Read the first streaming message to extract IDs
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    try:
                        data_str = line[6:]  # Remove "data: " prefix
                        data = json.loads(data_str)

                        # Check if this is the workflow_started event
                        if data.get("event") == "workflow_started":
                            workflow_run_id = data.get("workflow_run_id")
                            task_id = data.get("task_id")

                            if workflow_run_id and task_id:
                                logger.info(f"Started streaming workflow: {workflow_run_id}, task: {task_id}")
                                return workflow_run_id, task_id
                            else:
                                raise ValueError("workflow_run_id or task_id not found in workflow_started event")

                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse streaming data: {e}")
                        continue

            raise ValueError("workflow_started event not found in streaming response")


async def cv_parse_dify(
    upload_file_id: str,
    api_token: str | None = None,
    user: str = "campaign_hiring",
    base_url: str | None = None,
    timeout: float = 60.0,
    response_mode: str = "streaming",
) -> dict[str, any]:
    """
    Parse a CV using Dify workflow API.

    Args:
        upload_file_id: ID of the uploaded file from upload_file_to_dify
        api_token: Dify API token (Bearer token). If None, uses settings.DIFY_API_TOKEN
        user: User identifier for the request
        base_url: Base URL for the Dify API. If None, uses settings.DIFY_BASE_URL
        timeout: Request timeout in seconds (default 60s for CV parsing)
        response_mode: Response mode ("blocking" or "streaming")

    Returns:
        Dict containing the workflow execution result with parsed CV data

    Raises:
        httpx.HTTPStatusError: If the API request fails
        ValueError: If api_token is not provided and not set in config
    """
    # Use config defaults if not provided
    if api_token is None:
        api_token = settings.DIFY_API_TOKEN
    if base_url is None:
        base_url = settings.DIFY_BASE_URL

    if not api_token:
        raise ValueError("Dify API token is required. Set DIFY_API_TOKEN in environment or pass api_token parameter.")

    url = f"{base_url}/v1/workflows/run"
    headers = {"Authorization": f"Bearer {api_token}", "Content-Type": "application/json"}

    payload = {
        "inputs": {"cv_file": {"transfer_method": "local_file", "upload_file_id": upload_file_id, "type": "document"}},
        "response_mode": response_mode,
        "user": user,
    }

    if response_mode == "streaming":
        async with httpx.AsyncClient(timeout=timeout) as client:
            async with client.stream("POST", url=url, headers=headers, json=payload) as response:
                response.raise_for_status()

                # Process streaming messages to find the final result
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data_str = line[6:]  # Remove "data: " prefix
                            data = json.loads(data_str)

                            # Check if this is the workflow_finished event
                            if data.get("event") == "workflow_finished":
                                logger.info(f"Workflow finished: {data.get('workflow_run_id')}")
                                return data  # Return the complete workflow_finished data

                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse streaming data: {e}")
                            continue

                raise ValueError("workflow_finished event not found in streaming response")
    else:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url=url, headers=headers, json=payload)

            response.raise_for_status()
            return response.json()


async def upload_and_parse_cv(
    file_path: str | Path,
    api_token: str | None = None,
    user: str = "campaign_hiring",
    base_url: str | None = None,
    upload_timeout: float = 30.0,
    parse_timeout: float = 60.0,
    response_mode: str = "blocking",
) -> dict[str, any]:
    """
    Upload a CV file and parse it in one operation.

    Args:
        file_path: Path to the CV file to upload and parse
        api_token: Dify API token (Bearer token). If None, uses settings.DIFY_API_TOKEN
        user: User identifier for the request
        base_url: Base URL for the Dify API. If None, uses settings.DIFY_BASE_URL
        upload_timeout: Timeout for file upload in seconds
        parse_timeout: Timeout for CV parsing in seconds
        response_mode: Response mode ("blocking" or "streaming")

    Returns:
        Dict containing the workflow execution result with parsed CV data

    Raises:
        httpx.HTTPStatusError: If any API request fails
        FileNotFoundError: If the file doesn't exist
        ValueError: If api_token is not provided and not set in config
    """
    # First upload the file
    upload_result = await upload_file_to_dify(
        file_path=file_path, api_token=api_token, base_url=base_url, timeout=upload_timeout
    )

    # Then parse the uploaded file
    parse_result = await cv_parse_dify(
        upload_file_id=upload_result["id"],
        api_token=api_token,
        user=user,
        base_url=base_url,
        timeout=parse_timeout,
        response_mode=response_mode,
    )

    return parse_result


# FastAPI Router and Models
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/internal/v1", tags=["dify-cv-parser"])


class DifyCVParseResponse(BaseModel):
    """Response model for Dify CV parsing"""

    task_id: str
    workflow_run_id: str
    status: str
    success: bool
    profile: dict
    meta_data: dict


class CallbackPayload(BaseModel):
    """Payload sent to callback URL"""

    task_id: str
    workflow_run_id: str
    status: str
    success: bool
    profile: dict
    meta_data: dict
    original_filename: str


async def send_callback(callback_url: str, payload: CallbackPayload) -> None:
    """Send callback notification to the provided URL"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                callback_url, json=payload.model_dump(), headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            logger.info(f"Callback sent successfully to {callback_url}")
    except Exception as e:
        logger.error(f"Failed to send callback to {callback_url}: {str(e)}")


async def process_cv_with_callback(
    file_path: Path, original_filename: str, api_token: str, callback_url: str | None = None
) -> None:
    """Process CV and send callback if URL is provided"""
    try:
        # Parse the CV using Dify
        result = await upload_and_parse_cv(file_path=file_path, api_token=api_token)

        # Extract data from result
        data = result.get("data", {})
        outputs = data.get("outputs", {})
        result_data = outputs.get("result", {})

        # Create callback payload
        payload = CallbackPayload(
            task_id=result.get("task_id", ""),
            workflow_run_id=result.get("workflow_run_id", ""),
            status=data.get("status", "unknown"),
            success=result_data.get("success", False),
            profile=result_data.get("profile", {}),
            meta_data=result_data.get("meta_data", {}),
            elapsed_time=result.get("elapsed_time", 0.0),
            total_tokens=result.get("total_tokens", 0),
            total_steps=result.get("total_steps", 0),
            created_at=result.get("created_at", 0),
            finished_at=result.get("finished_at", 0),
            original_filename=original_filename,
        )

        # Send callback if URL is provided
        if callback_url:
            await send_callback(callback_url, payload)

    except Exception as e:
        logger.error(f"Error processing CV {original_filename}: {str(e)}")

        # Send error callback if URL is provided
        if callback_url:
            error_payload = CallbackPayload(
                task_id="",
                workflow_run_id="",
                status="failed",
                success=False,
                profile={},
                meta_data={"error": str(e)},
                elapsed_time=0.0,
                total_tokens=0,
                total_steps=0,
                created_at=0,
                finished_at=0,
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
    finally:
        # Clean up temporary file
        try:
            file_path.unlink(missing_ok=True)
        except Exception as e:
            logger.warning(f"Failed to delete temporary file {file_path}: {str(e)}")


async def process_cv_streaming_with_callback(
    upload_file_id: str, original_filename: str, workflow_run_id: str, task_id: str, callback_url: str
) -> None:
    """Process CV with streaming and send callback when complete"""
    try:
        # Continue processing the already started streaming workflow
        result = await cv_parse_dify(upload_file_id=upload_file_id, response_mode="streaming")

        # Extract data from result (workflow_finished event structure)
        data = result.get("data", {})
        outputs = data.get("outputs", {})
        result_data = outputs.get("result", {})

        # Create callback payload
        payload = CallbackPayload(
            task_id=task_id,
            workflow_run_id=workflow_run_id,
            status=data.get("status", "unknown"),
            success=result_data.get("success", False),
            profile=result_data.get("profile", {}),
            meta_data=result_data.get("meta_data", {}),
            elapsed_time=result.get("elapsed_time", 0.0),
            total_tokens=result.get("total_tokens", 0),
            total_steps=result.get("total_steps", 0),
            created_at=result.get("created_at", 0),
            finished_at=result.get("finished_at", 0),
            original_filename=original_filename,
        )

        # Send callback
        await send_callback(callback_url, payload)

    except Exception as e:
        logger.error(f"Error processing streaming CV {original_filename}: {str(e)}")

        # Send error callback
        error_payload = CallbackPayload(
            task_id=task_id,
            workflow_run_id=workflow_run_id,
            status="failed",
            success=False,
            profile={},
            meta_data={"error": str(e)},
            elapsed_time=0.0,
            total_tokens=0,
            total_steps=0,
            created_at=0,
            finished_at=0,
            original_filename=original_filename,
        )
        await send_callback(callback_url, error_payload)


@router.post("/parse", response_model=DifyCVParseResponse)
async def parse_cv_dify(
    background_tasks: BackgroundTasks,
    file: UploadFile | None = File(None, description="CV file to parse (PDF, DOC, or DOCX)"),
    callback_url: HttpUrl | None = Form(None, description="Optional callback URL to receive results"),
) -> DifyCVParseResponse:
    """
    Parse CV using Dify API with optional callback notification.

    This endpoint:
    1. Accepts a CV file upload (PDF, DOC, or DOCX)
    2. Uploads the file to Dify API
    3. Processes the CV using Dify workflow
    4. Returns the parsed results immediately (if no callback URL)
    5. Optionally sends results to a callback URL (async processing)

    Configuration is read from environment variables:
    - DIFY_API_TOKEN: Dify API Bearer token
    - DIFY_BASE_URL: Dify API base URL

    Args:
        file: CV file to parse
        callback_url: Optional URL to receive parsing results

    Returns:
        Parsed CV data from Dify workflow
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_ext} not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}",
            )

        # Check file size
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400, detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
            )

        # Reset file pointer
        await file.seek(0)

        logger.info(f"Received CV file for Dify parsing: {file.filename}")

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_file.write(file_content)
            temp_file_path = Path(temp_file.name)

        # First upload the file to get upload_file_id
        upload_result = await upload_file_to_dify(file_path=temp_file_path)
        upload_file_id = upload_result["id"]

        # Start streaming workflow to get workflow_run_id and task_id
        workflow_run_id, task_id = await start_cv_parse_streaming(upload_file_id=upload_file_id)

        # If no callback URL, process synchronously (but still use streaming internally)
        if not callback_url:
            try:
                # Continue with full streaming processing
                result = await cv_parse_dify(upload_file_id=upload_file_id, response_mode="streaming")

                # Clean up temp file
                temp_file_path.unlink(missing_ok=True)

                # Extract and return response data
                data = result.get("data", {})
                outputs = data.get("outputs", {})
                result_data = outputs.get("result", {})

                response = DifyCVParseResponse(
                    task_id=task_id,
                    workflow_run_id=workflow_run_id,
                    status=data.get("status", "unknown"),
                    success=result_data.get("success", False),
                    profile=result_data.get("profile", {}),
                    meta_data=result_data.get("meta_data", {}),
                    elapsed_time=result.get("elapsed_time", 0.0),
                    total_tokens=result.get("total_tokens", 0),
                    total_steps=result.get("total_steps", 0),
                    created_at=result.get("created_at", 0),
                    finished_at=result.get("finished_at", 0),
                )

                logger.info(f"Successfully parsed CV: {file.filename}")
                return response

            except Exception as e:
                # Clean up temp file on error
                temp_file_path.unlink(missing_ok=True)
                raise

        else:
            # Process asynchronously with callback - workflow already started
            background_tasks.add_task(
                process_cv_streaming_with_callback,
                upload_file_id,
                file.filename,
                workflow_run_id,
                task_id,
                str(callback_url),
            )

            # Clean up temp file since we have the upload_file_id
            temp_file_path.unlink(missing_ok=True)

            # Return immediate response with real IDs
            return DifyCVParseResponse(
                task_id=task_id,
                workflow_run_id=workflow_run_id,
                status="processing",
                success=True,
                profile={},
                meta_data={},
                elapsed_time=0.0,
                total_tokens=0,
                total_steps=0,
                created_at=0,
                finished_at=0,
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing CV {file.filename}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
