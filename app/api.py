import logging

from fastapi import <PERSON><PERSON><PERSON>, File, Form, HTTPException, Request, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.api_dify import router as dify_router
from app.config import settings
from app.file_processors import FileProcessor, get_file_base64, process_file
from app.models import CVData, ParseRequest
from app.parser import CVParserState, cv_parser_workflow

# Set up logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title=settings.API_TITLE, version=settings.API_VERSION, description=settings.API_DESCRIPTION)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(dify_router)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "CV Parser API",
        "version": settings.API_VERSION,
        "endpoints": {
            "parse": "/v2/parse",
            "health": "/health",
            "dify_parse": "/v1/parse",
        },
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": settings.API_VERSION, "model": settings.OPENAI_MODEL}


@app.post("/v2/parse", response_model=CVData)
async def parse_cv(
    request: Request,
    file: UploadFile | None = File(None, description="CV file to parse (PDF, DOC, or DOCX)"),
    multimodal: bool = Form(False, description="Use multimodal vision parsing for files"),
) -> CVData:
    """
    Parse CV/Resume using text extraction or multimodal vision parsing

    This endpoint accepts:
    1. File upload (PDF, DOC, or DOCX) with text extraction (default)
    2. File upload with multimodal vision parsing (set multimodal=true)
    3. Raw text in JSON body - parses directly

    Returns structured CV data in all cases.
    """
    try:
        # Handle JSON request body for text parsing
        request_body = None
        if not file:
            # Try to parse JSON body
            content_type = request.headers.get("content-type", "").lower()
            if "application/json" in content_type:
                try:
                    body_bytes = await request.body()
                    if body_bytes:
                        import json

                        body_dict = json.loads(body_bytes.decode())
                        request_body = ParseRequest(**body_dict)
                except Exception as e:
                    logger.error(f"Failed to parse JSON body: {e}")
                    raise HTTPException(status_code=422, detail="Invalid JSON format")

        # Validate that at least one input is provided
        if not file and not request_body:
            raise HTTPException(
                status_code=400,
                detail="Either file upload or text in request body must be provided",
            )

        # Determine parsing mode
        use_multimodal = False
        if file:
            # File upload mode - check multimodal flag
            use_multimodal = multimodal
        elif request_body:
            # JSON mode - check multimodal in request body (only applies to future file uploads)
            use_multimodal = request_body.multimodal and file is not None

        # Check multimodal settings
        if use_multimodal and not settings.ENABLE_MULTIMODAL:
            raise HTTPException(status_code=403, detail="Multimodal parsing is disabled in configuration")

        # Process input based on mode
        if file and use_multimodal:
            # Multimodal file processing
            logger.info(f"Received file for multimodal parsing: {file.filename}")

            # Validate file
            FileProcessor.validate_file(file, settings.ALLOWED_EXTENSIONS, settings.MAX_FILE_SIZE)

            # Get file as base64
            file_base64, mime_type = await get_file_base64(file)
            logger.info(f"Converted file to base64, size: {len(file_base64)} chars")

            # Create initial state for multimodal
            initial_state: CVParserState = {
                "input_text": None,
                "input_file_base64": file_base64,
                "filename": file.filename,
                "parsed_data": None,
                "error": None,
                "mode": "multimodal",
            }

        elif file:
            # Text extraction file processing
            logger.info(f"Received file: {file.filename}")
            text, filename = await process_file(file, settings.ALLOWED_EXTENSIONS, settings.MAX_FILE_SIZE)
            logger.info(f"Extracted {len(text)} characters from {filename}")

            # Create initial state for text
            initial_state: CVParserState = {
                "input_text": text,
                "input_file_base64": None,
                "filename": filename,
                "parsed_data": None,
                "error": None,
                "mode": "text",
            }

        else:
            # Raw text processing
            logger.info("Received raw text input")
            text = request_body.text
            filename = request_body.filename or "raw_text_input.txt"
            logger.info(f"Received {len(text)} characters of raw text")

            # Create initial state for text
            initial_state: CVParserState = {
                "input_text": text,
                "input_file_base64": None,
                "filename": filename,
                "parsed_data": None,
                "error": None,
                "mode": "text",
            }

        # Run the workflow
        logger.info(
            f"Starting CV parsing workflow for file: {initial_state.get('filename')} in mode: {initial_state.get('mode')}"
        )
        result = await cv_parser_workflow.ainvoke(initial_state)

        # Check for errors
        if result.get("errors") and result["errors"]:
            error_msg = "; ".join(result["errors"])
            logger.error(f"Parsing errors: {error_msg}")
            raise HTTPException(status_code=400, detail=f"Parsing failed: {error_msg}")

        if result.get("error"):
            logger.error(f"Parsing error: {result['error']}")
            raise HTTPException(status_code=400, detail=result["error"])

        # Return parsed data
        parsed_data = result.get("parsed_data")
        if not parsed_data:
            raise HTTPException(status_code=500, detail="Failed to parse CV data")

        mode_str = "multimodal" if use_multimodal else "text extraction"
        logger.info(
            f"Successfully parsed CV ({mode_str}) for: {parsed_data.personal_info.full_name if parsed_data.personal_info else 'Unknown'}"
        )
        return parsed_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(status_code=exc.status_code, content={"error": exc.detail, "status_code": exc.status_code})


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(status_code=500, content={"error": "Internal server error", "status_code": 500})


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080)
