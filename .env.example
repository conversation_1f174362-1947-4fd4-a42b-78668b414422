# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Optional: OpenAI Model Configuration
# Default: gpt-4o-mini (recommended for CV parsing)
OPENAI_MODEL=gpt-4o-mini

# Optional: API Configuration
# These have defaults but can be customized
MAX_FILE_SIZE=10485760  # 10MB in bytes
TEMPERATURE=0.1  # Low temperature for consistent parsing
MAX_RETRIES=3

# Optional: Feature Toggles
ENABLE_MULTIMODAL=true

# Optional: PDF to Image Conversion (for multimodal parsing)
PDF_TO_IMAGE_DPI=200  # DPI for PDF to image conversion (higher = better quality, larger files)
PDF_MAX_PAGES=10      # Maximum pages to process (prevents excessive API costs)
PDF_IMAGE_FORMAT=PNG  # PNG (better quality) or JPEG (smaller size)

# Optional: Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERRO<PERSON>, CR<PERSON><PERSON>AL